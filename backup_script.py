#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت حفظ التحديثات - لضمان عدم فقدان التحديثات لجميع ملفات المشروع
"""

import os
import shutil
import datetime

def get_all_python_files():
    """الحصول على جميع ملفات Python في المشروع"""
    python_files = []
    
    # البحث في المجلد الحالي
    for item in os.listdir('.'):
        if item.endswith('.py') and os.path.isfile(item):
            python_files.append(item)
    
    # إضافة ملفات أخرى مهمة
    other_important_files = [
        'main_window.spec',
        '01.ico',
        'data.db',
        'date2025/data.db'
    ]
    
    for file in other_important_files:
        if os.path.exists(file):
            python_files.append(file)
    
    # إضافة ملفات من مجلدات فرعية مهمة
    subdirs = ['fonts', 'temp', 'المخالفات', 'تقارير_زيارة_الطبيب']
    for subdir in subdirs:
        if os.path.exists(subdir) and os.path.isdir(subdir):
            for root, dirs, files in os.walk(subdir):
                for file in files:
                    file_path = os.path.join(root, file)
                    python_files.append(file_path)
    
    return sorted(python_files)

def backup_all_files():
    """إنشاء نسخة احتياطية من جميع ملفات المشروع"""
    
    # الحصول على جميع الملفات
    all_files = get_all_python_files()
    
    # إنشاء مجلد النسخ الاحتياطية
    backup_dir = f"backup_complete_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    print("=" * 60)
    print("🛡️ سكريپت النسخ الاحتياطي الشامل")
    print("=" * 60)
    print(f"📂 إنشاء مجلد النسخ الاحتياطية: {backup_dir}")
    print(f"📊 عدد الملفات المراد نسخها: {len(all_files)}")
    print()
    
    success_count = 0
    error_count = 0
    
    for file in all_files:
        if os.path.exists(file):
            try:
                # إنشاء المجلدات الفرعية إذا لزم الأمر
                backup_file_path = os.path.join(backup_dir, file)
                backup_file_dir = os.path.dirname(backup_file_path)
                
                if backup_file_dir and not os.path.exists(backup_file_dir):
                    os.makedirs(backup_file_dir, exist_ok=True)
                
                shutil.copy2(file, backup_file_path)
                print(f"✅ تم نسخ {file}")
                success_count += 1
            except Exception as e:
                print(f"❌ خطأ في نسخ {file}: {e}")
                error_count += 1
        else:
            print(f"⚠️ الملف غير موجود: {file}")
    
    print()
    print("=" * 60)
    print(f"📈 إحصائيات النسخ الاحتياطي:")
    print(f"   ✅ نجح: {success_count} ملف")
    print(f"   ❌ فشل: {error_count} ملف")
    print(f"🎉 تم إنشاء النسخة الاحتياطية في: {backup_dir}")
    print("=" * 60)
    
    return backup_dir

def verify_database_config():
    """التحقق من وجود تحديثات database_config في الملفات الرئيسية"""
    
    files_to_check = [
        'sub1_window_html.py',
        'sub8_window.py',
        'main_window.py',
        'sub2_window_html.py',
        'sub3_window_html.py',
        'sub4_window.py',
        'sub5_window_html.py',
        'sub6_window_html.py',
        'sub7_window_html.py',
        'sub9_window.py',
        'sub10_window.py',
        'sub11_window_html.py',
        'sub12_window_html.py',
        'sub13_window_html.py',
        'sub14_window_html.py',
        'sub15_window_html.py',
        'sub16_window_html.py',
        'sub17_window_html.py',
        'sub18_window_html.py',
        'sub19_window_html.py',
        'sub20_window.py',
        'sub21_window.py',
        'sub37_window.py',
        'sub100_window.py',
        'sub210_window.py'
    ]
    
    print("🔍 التحقق من تحديثات database_config:")
    
    updated_files = 0
    non_updated_files = 0
    
    for file in files_to_check:
        if os.path.exists(file):
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'from database_config import' in content or 'get_database_path()' in content:
                        print(f"✅ {file}: محدث")
                        updated_files += 1
                    else:
                        print(f"⚠️ {file}: يحتاج تحديث")
                        non_updated_files += 1
            except Exception as e:
                print(f"❌ خطأ في قراءة {file}: {e}")
        else:
            print(f"⚠️ الملف غير موجود: {file}")
    
    print(f"\n📊 ملخص التحديثات:")
    print(f"   ✅ محدث: {updated_files} ملف")
    print(f"   ⚠️ يحتاج تحديث: {non_updated_files} ملف")

def create_project_summary():
    """إنشاء ملخص شامل للمشروع"""
    
    print("\n📋 ملخص المشروع:")
    
    # عدد ملفات Python
    python_files = [f for f in os.listdir('.') if f.endswith('.py')]
    print(f"   🐍 ملفات Python: {len(python_files)}")
    
    # عدد ملفات النوافذ
    window_files = [f for f in python_files if f.startswith('sub') and f.endswith('.py')]
    print(f"   🪟 ملفات النوافذ: {len(window_files)}")
    
    # ملفات الطباعة
    print_files = [f for f in python_files if f.startswith('print') and f.endswith('.py')]
    print(f"   🖨️ ملفات الطباعة: {len(print_files)}")
    
    # الملفات الرئيسية
    main_files = ['app.py', 'main_window.py', 'database_config.py', 'shortcut_dialog.py']
    existing_main = [f for f in main_files if os.path.exists(f)]
    print(f"   📁 الملفات الرئيسية: {len(existing_main)}/{len(main_files)}")
    
    # قاعدة البيانات
    if os.path.exists('date2025/data.db'):
        print(f"   💾 قاعدة البيانات: موجودة (date2025/data.db)")
    elif os.path.exists('data.db'):
        print(f"   💾 قاعدة البيانات: موجودة (data.db)")
    else:
        print(f"   ⚠️ قاعدة البيانات: غير موجودة")

if __name__ == "__main__":
    print("=" * 60)
    print("🛡️ سكريپت النسخ الاحتياطي الشامل للمشروع")
    print("=" * 60)
    
    # إنشاء نسخة احتياطية شاملة
    backup_dir = backup_all_files()
    
    print()
    
    # التحقق من تحديثات قاعدة البيانات
    verify_database_config()
    
    # إنشاء ملخص المشروع
    create_project_summary()
    
    print("\n" + "=" * 60)
    print("✨ تم الانتهاء من عملية النسخ الاحتياطي والتحقق")
    print(f"📂 النسخة الاحتياطية محفوظة في: {backup_dir}")
    print("=" * 60)
