# -*- coding: utf-8 -*-
"""
ملف إعدادات قاعدة البيانات
يحتوي على المسار الصحيح لقاعدة البيانات في جميع الحالات
"""

import os
import sys
import sqlite3


def get_database_path():
    """
    إرجاع المسار الصحيح لقاعدة البيانات
    
    في التطبيق المحزم: سيكون المسار في مجلد date2025 بجانب التطبيق
    في التطبيق العادي: سيكون المسار في مجلد date2025 بجانب الملفات
    
    Returns:
        str: المسار الكامل لقاعدة البيانات
    """
    
    if getattr(sys, 'frozen', False):
        # في حالة التطبيق المحزم (بعد PyInstaller)
        # نحصل على مجلد التطبيق التنفيذي
        app_dir = os.path.dirname(sys.executable)
        print(f"INFO: التطبيق محزم - مجلد التطبيق: {app_dir}")
    else:
        # في حالة التشغيل العادي من Python
        # نحصل على مجلد الملفات الحالي
        app_dir = os.path.dirname(os.path.abspath(__file__))
        print(f"INFO: التطبيق غير محزم - مجلد الملفات: {app_dir}")
    
    # إنشاء مجلد date2025 إذا لم يكن موجوداً
    data_folder = os.path.join(app_dir, "date2025")
    if not os.path.exists(data_folder):
        os.makedirs(data_folder)
        print(f"INFO: تم إنشاء مجلد البيانات: {data_folder}")
    
    # مسار قاعدة البيانات
    db_path = os.path.join(data_folder, "data.db")
    print(f"INFO: مسار قاعدة البيانات: {db_path}")
    
    return db_path


def get_database_connection():
    """
    إرجاع اتصال جديد بقاعدة البيانات
    
    Returns:
        sqlite3.Connection: اتصال بقاعدة البيانات
    """
    db_path = get_database_path()
    try:
        conn = sqlite3.connect(db_path)
        print(f"INFO: تم الاتصال بقاعدة البيانات بنجاح: {db_path}")
        return conn
    except Exception as e:
        print(f"ERROR: فشل الاتصال بقاعدة البيانات: {e}")
        raise


def ensure_database_exists():
    """
    التأكد من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة
    """
    db_path = get_database_path()
    
    if not os.path.exists(db_path):
        print(f"INFO: قاعدة البيانات غير موجودة، سيتم إنشاؤها: {db_path}")
        
        # نسخ قاعدة البيانات الموجودة إذا كانت متوفرة
        current_dir = os.path.dirname(os.path.abspath(__file__))
        old_db_path = os.path.join(current_dir, "data.db")
        
        if os.path.exists(old_db_path):
            import shutil
            shutil.copy2(old_db_path, db_path)
            print(f"INFO: تم نسخ قاعدة البيانات من {old_db_path} إلى {db_path}")
        else:
            # إنشاء قاعدة بيانات فارغة
            conn = sqlite3.connect(db_path)
            conn.close()
            print(f"INFO: تم إنشاء قاعدة بيانات فارغة: {db_path}")
    else:
        print(f"INFO: قاعدة البيانات موجودة: {db_path}")


if __name__ == "__main__":
    # اختبار الوحدة
    print("=== اختبار وحدة إعدادات قاعدة البيانات ===")
    
    # التأكد من وجود قاعدة البيانات
    ensure_database_exists()
    
    # اختبار الحصول على المسار
    db_path = get_database_path()
    print(f"مسار قاعدة البيانات: {db_path}")
    
    # اختبار الاتصال
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"الجداول الموجودة: {tables}")
        conn.close()
        print("اختبار الاتصال نجح!")
    except Exception as e:
        print(f"فشل اختبار الاتصال: {e}")
