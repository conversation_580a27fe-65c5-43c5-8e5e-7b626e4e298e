import sqlite3
import os

# الاتصال بقاعدة البيانات
db_path = "date2025/data.db"
if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print("=== فحص قاعدة البيانات ===")
    
    # فحص السنوات الدراسية الموجودة
    cursor.execute("SELECT DISTINCT السنة_الدراسية FROM جدول_عام")
    years = cursor.fetchall()
    print(f"السنوات الدراسية المتاحة: {[year[0] for year in years]}")
    
    # فحص الأقسام للسنة 2024-2025
    cursor.execute("SELECT DISTINCT القسم FROM جدول_عام WHERE السنة_الدراسية = '2024-2025'")
    sections_2024 = cursor.fetchall()
    print(f"الأقسام للسنة 2024-2025: {[section[0] for section in sections_2024 if section[0]]}")
    
    # فحص جميع الأقسام
    cursor.execute("SELECT DISTINCT القسم FROM جدول_عام")
    all_sections = cursor.fetchall()
    print(f"جميع الأقسام: {[section[0] for section in all_sections if section[0]]}")
    
    # عدد الطلاب في كل قسم
    cursor.execute("SELECT القسم, COUNT(*) FROM جدول_عام GROUP BY القسم")
    section_counts = cursor.fetchall()
    print("\nعدد الطلاب في كل قسم:")
    for section, count in section_counts:
        if section:
            print(f"  {section}: {count} طالب")
    
    # عينة من البيانات
    cursor.execute("SELECT القسم, الاسم_والنسب, السنة_الدراسية FROM جدول_عام LIMIT 5")
    sample_data = cursor.fetchall()
    print("\nعينة من البيانات:")
    for row in sample_data:
        print(f"  القسم: {row[0]}, الاسم: {row[1]}, السنة: {row[2]}")
    
    conn.close()
else:
    print(f"ملف قاعدة البيانات غير موجود: {db_path}")
