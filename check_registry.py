import sqlite3
from database_config import get_database_path

db_path = get_database_path()
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

print('هيكل جدول السجل_العام:')
cursor.execute('PRAGMA table_info(السجل_العام)')
for row in cursor.fetchall():
    print(f'  - {row[1]} ({row[2]})')

print('\nنموذج من بيانات السجل_العام:')
cursor.execute('SELECT * FROM السجل_العام LIMIT 3')
columns = [description[0] for description in cursor.description]
print(f'الأعمدة: {columns}')
for row in cursor.fetchall():
    print(f'  {row}')

conn.close()
