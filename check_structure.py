import sqlite3
from database_config import get_database_path

db_path = get_database_path()
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

print('جداول قاعدة البيانات:')
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
for table in tables:
    print(f'  - {table[0]}')

print('\nهيكل جدول البنية_التربوية:')
cursor.execute('PRAGMA table_info(البنية_التربوية)')
for row in cursor.fetchall():
    print(f'  - {row[1]} ({row[2]})')

print('\nنموذج من بيانات البنية_التربوية:')
cursor.execute('SELECT * FROM البنية_التربوية LIMIT 3')
for row in cursor.fetchall():
    print(f'  {row}')

print('\nنموذج من بيانات الأقسام المسندة:')
cursor.execute('SELECT القسم, الأقسام_المسندة FROM البنية_التربوية WHERE الأقسام_المسندة IS NOT NULL LIMIT 5')
for row in cursor.fetchall():
    print(f'  القسم: {row[0]}, مسند لحراسة: {row[1]}')

conn.close()
