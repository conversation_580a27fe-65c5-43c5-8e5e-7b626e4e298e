import sqlite3
from database_config import get_database_path

db_path = get_database_path()
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

print('هيكل جدول اللوائح:')
cursor.execute('PRAGMA table_info(اللوائح)')
for row in cursor.fetchall():
    print(f'  - {row[1]} ({row[2]})')

print('\nنموذج من بيانات اللوائح:')
cursor.execute('SELECT * FROM اللوائح LIMIT 3')
columns = [description[0] for description in cursor.description]
print(f'الأعمدة: {columns}')
for row in cursor.fetchall():
    print(f'  {row}')

print('\nفحص العلاقة بين الجداول:')
cursor.execute('''
    SELECT س.الرمز, س.الاسم_والنسب, ل.القسم, ل.السنة_الدراسية
    FROM السجل_العام س
    JOIN اللوائح ل ON س.الرمز = ل.الرمز
    LIMIT 3
''')
for row in cursor.fetchall():
    print(f'  الرمز: {row[0]}, الاسم: {row[1]}, القسم: {row[2]}, السنة: {row[3]}')

conn.close()
