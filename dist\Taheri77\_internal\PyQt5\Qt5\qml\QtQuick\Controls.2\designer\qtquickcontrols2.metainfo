MetaInfo {
    Type {
        name: "QtQuick.Controls.BusyIndicator"
        icon: "images/busyindicator-icon16.png"

        ItemLibraryEntry {
            name: "Busy Indicator"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/busyindicator-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"
        }
    }

    Type {
        name: "QtQuick.Controls.Button"
        icon: "images/button-icon16.png"

        ItemLibraryEntry {
            name: "Button"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/button-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "text"; type: "binding"; value: "qsTr(\"Button\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.CheckBox"
        icon: "images/checkbox-icon16.png"

        ItemLibraryEntry {
            name: "Check Box"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/checkbox-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "text"; type: "binding"; value: "qsTr(\"Check Box\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.CheckDelegate"
        icon: "images/checkbox-icon16.png"

        ItemLibraryEntry {
            name: "Check Delegate"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/checkbox-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "text"; type: "binding"; value: "qsTr(\"Check Delegate\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.ComboBox"
        icon: "images/combobox-icon16.png"

        ItemLibraryEntry {
            name: "Combo Box"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/combobox-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"
        }
    }

    Type {
        name: "QtQuick.Controls.DelayButton"
        icon: "images/button-icon16.png"

        ItemLibraryEntry {
            name: "Delay Button"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/delaybutton-icon.png"
            version: "2.2"
            requiredImport: "QtQuick.Controls"

            Property { name: "text"; type: "binding"; value: "qsTr(\"Delay Button\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.Dial"
        icon: "images/dial-icon16.png"

        ItemLibraryEntry {
            name: "Dial"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/dial-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"
        }
    }

    Type {
        name: "QtQuick.Controls.Frame"
        icon: "images/frame-icon16.png"

        ItemLibraryEntry {
            name: "Frame"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/frame-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "width"; type: "int"; value: 200 }
            Property { name: "height"; type: "int"; value: 200 }
        }
    }

    Type {
        name: "QtQuick.Controls.GroupBox"
        icon: "images/groupbox-icon16.png"

        ItemLibraryEntry {
            name: "Group Box"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/groupbox-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "width"; type: "int"; value: 200 }
            Property { name: "height"; type: "int"; value: 200 }
            Property { name: "title"; type: "binding"; value: "qsTr(\"Group Box\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.ItemDelegate"
        icon: "images/itemdelegate-icon16.png"

        ItemLibraryEntry {
            name: "Item Delegate"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/itemdelegate-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "text"; type: "binding"; value: "qsTr(\"Item Delegate\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.Label"
        icon: "images/label-icon16.png"

        ItemLibraryEntry {
            name: "Label"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/label-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "text"; type: "binding"; value: "qsTr(\"Label\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.Page"
        icon: "images/page-icon16.png"

        ItemLibraryEntry {
            name: "Page"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/page-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "width"; type: "int"; value: 200 }
            Property { name: "height"; type: "int"; value: 200 }
        }
    }

    Type {
        name: "QtQuick.Controls.PageIndicator"
        icon: "images/pageindicator-icon16.png"

        ItemLibraryEntry {
            name: "Page Indicator"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/pageindicator-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "count"; type: "int"; value: 3 }
        }
    }

    Type {
        name: "QtQuick.Controls.Pane"
        icon: "images/pane-icon16.png"

        ItemLibraryEntry {
            name: "Pane"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/pane-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "width"; type: "int"; value: 200 }
            Property { name: "height"; type: "int"; value: 200 }
        }
    }

    Type {
        name: "QtQuick.Controls.ProgressBar"
        icon: "images/progressbar-icon16.png"

        ItemLibraryEntry {
            name: "Progress Bar"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/progressbar-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "value"; type: "real"; value: 0.5 }
        }
    }

    Type {
        name: "QtQuick.Controls.RadioButton"
        icon: "images/radiobutton-icon16.png"

        ItemLibraryEntry {
            name: "Radio Button"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/radiobutton-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "text"; type: "binding"; value: "qsTr(\"Radio Button\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.RadioDelegate"
        icon: "images/radiobutton-icon16.png"

        ItemLibraryEntry {
            name: "Radio Delegate"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/radiobutton-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "text"; type: "binding"; value: "qsTr(\"Radio Delegate\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.RangeSlider"
        icon: "images/rangeslider-icon16.png"

        ItemLibraryEntry {
            name: "Range Slider"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/rangeslider-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "first.value"; type: "real"; value: 0.25 }
            Property { name: "second.value"; type: "real"; value: 0.75 }
        }
    }

    Type {
        name: "QtQuick.Controls.RoundButton"
        icon: "images/roundbutton-icon16.png"

        ItemLibraryEntry {
            name: "Round Button"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/roundbutton-icon.png"
            version: "2.1"
            requiredImport: "QtQuick.Controls"
            Property { name: "text"; type: "string"; value: "+" }
        }
    }

    Type {
        name: "QtQuick.Controls.Slider"
        icon: "images/slider-icon16.png"

        ItemLibraryEntry {
            name: "Slider"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/slider-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "value"; type: "real"; value: 0.5 }
        }
    }

    Type {
        name: "QtQuick.Controls.SpinBox"
        icon: "images/spinbox-icon16.png"

        ItemLibraryEntry {
            name: "Spin Box"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/spinbox-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"
        }
    }

    Type {
        name: "QtQuick.Controls.ScrollView"
        icon: "images/scrollview-icon16.png"

        ItemLibraryEntry {
            name: "Scroll View"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/scrollview-icon.png"
            version: "2.2"
            requiredImport: "QtQuick.Controls"

            Property { name: "width"; type: "int"; value: 200 }
            Property { name: "height"; type: "int"; value: 200 }
        }
    }

    Type {
        name: "QtQuick.Controls.StackView"
        icon: "images/stackview-icon16.png"

        ItemLibraryEntry {
            name: "Stack View"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/stackview-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "width"; type: "int"; value: 200 }
            Property { name: "height"; type: "int"; value: 200 }
        }
    }

    Type {
        name: "QtQuick.Controls.SwipeDelegate"
        icon: "images/itemdelegate-icon16.png"

        ItemLibraryEntry {
            name: "Swipe Delegate"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/itemdelegate-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "text"; type: "binding"; value: "qsTr(\"Swipe Delegate\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.SwipeView"
        icon: "images/swipeview-icon16.png"

        ItemLibraryEntry {
            name: "Swipe View"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/swipeview-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "width"; type: "int"; value: 200 }
            Property { name: "height"; type: "int"; value: 200 }
        }
    }

    Type {
        name: "QtQuick.Controls.Switch"
        icon: "images/switch-icon16.png"

        ItemLibraryEntry {
            name: "Switch"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/switch-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "text"; type: "binding"; value: "qsTr(\"Switch\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.SwitchDelegate"
        icon: "images/switch-icon16.png"

        ItemLibraryEntry {
            name: "Switch Delegate"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/switch-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "text"; type: "binding"; value: "qsTr(\"Switch Delegate\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.TabBar"
        icon: "images/toolbar-icon16.png"

        ItemLibraryEntry {
            name: "Tab Bar"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/toolbar-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"
            Property { name: "width"; type: "int"; value: 240 }
        }
    }

    Type {
        name: "QtQuick.Controls.TabButton"
        icon: "images/toolbutton-icon16.png"

        ItemLibraryEntry {
            name: "Tab Button"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/toolbutton-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"
            Property { name: "text"; type: "binding"; value: "qsTr(\"Tab Button\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.TextArea"
        icon: "images/textarea-icon16.png"

        ItemLibraryEntry {
            name: "Text Area"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/textarea-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "placeholderText"; type: "binding"; value: "qsTr(\"Text Area\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.TextField"
        icon: "images/textfield-icon16.png"

        ItemLibraryEntry {
            name: "Text Field"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/textfield-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "placeholderText"; type: "binding"; value: "qsTr(\"Text Field\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.ToolBar"
        icon: "images/toolbar-icon16.png"

        ItemLibraryEntry {
            name: "Tool Bar"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/toolbar-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "width"; type: "int"; value: 360 }
        }
    }

    Type {
        name: "QtQuick.Controls.ToolButton"
        icon: "images/toolbutton-icon16.png"

        ItemLibraryEntry {
            name: "Tool Button"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/toolbutton-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "text"; type: "binding"; value: "qsTr(\"Tool Button\")" }
        }
    }

    Type {
        name: "QtQuick.Controls.ToolSeparator"
        icon: "images/toolseparator-icon16.png"

        ItemLibraryEntry {
            name: "Tool Separator"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/toolseparator-icon.png"
            version: "2.1"
            requiredImport: "QtQuick.Controls"
        }
    }

    Type {
        name: "QtQuick.Controls.Tumbler"
        icon: "images/tumbler-icon16.png"

        ItemLibraryEntry {
            name: "Tumbler"
            category: "Qt Quick - Controls 2"
            libraryIcon: "images/tumbler-icon.png"
            version: "2.0"
            requiredImport: "QtQuick.Controls"

            Property { name: "model"; type: "int"; value: "10" }
        }
    }
}
