import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable QtQuick.Timeline 1.0'

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        name: "QQuickKeyframe"
        prototype: "QObject"
        exports: ["QtQuick.Timeline/Keyframe 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "frame"; type: "double" }
        Property { name: "easing"; type: "QEasingCurve" }
        Property { name: "value"; type: "QVariant" }
        Signal { name: "easingCurveChanged" }
    }
    Component {
        name: "QQuickKeyframeGroup"
        defaultProperty: "keyframes"
        prototype: "QObject"
        exports: ["QtQuick.Timeline/KeyframeGroup 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "target"; type: "QObject"; isPointer: true }
        Property { name: "property"; type: "string" }
        Property { name: "keyframes"; type: "QQuickKeyframe"; isList: true; isReadonly: true }
    }
    Component {
        name: "QQuickTimeline"
        defaultProperty: "keyframeGroups"
        prototype: "QObject"
        exports: ["QtQuick.Timeline/Timeline 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "startFrame"; type: "double" }
        Property { name: "endFrame"; type: "double" }
        Property { name: "currentFrame"; type: "double" }
        Property { name: "keyframeGroups"; type: "QQuickKeyframeGroup"; isList: true; isReadonly: true }
        Property { name: "animations"; type: "QQuickTimelineAnimation"; isList: true; isReadonly: true }
        Property { name: "enabled"; type: "bool" }
    }
    Component {
        name: "QQuickTimelineAnimation"
        prototype: "QQuickNumberAnimation"
        exports: ["QtQuick.Timeline/TimelineAnimation 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "pingPong"; type: "bool" }
        Signal { name: "finished" }
    }
}
