import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable Qt.labs.calendar 1.0'

Module {
    dependencies: ["QtQuick 2.12"]
    Component {
        name: "QAbstractItemModel"
        prototype: "QObject"
        Enum {
            name: "LayoutChangeHint"
            values: {
                "NoLayoutChangeHint": 0,
                "VerticalSortHint": 1,
                "HorizontalSortHint": 2
            }
        }
        Enum {
            name: "CheckIndexOption"
            values: {
                "NoOption": 0,
                "IndexIsValid": 1,
                "DoNotUseParent": 2,
                "ParentIsInvalid": 4
            }
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
            Parameter { name: "roles"; type: "QVector<int>" }
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
        }
        Signal {
            name: "headerDataChanged"
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
        }
        Signal { name: "layoutChanged" }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
        }
        Signal { name: "layoutAboutToBeChanged" }
        Signal {
            name: "rowsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal { name: "modelAboutToBeReset" }
        Signal { name: "modelReset" }
        Signal {
            name: "rowsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationRow"; type: "int" }
        }
        Signal {
            name: "rowsMoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
            Parameter { name: "destination"; type: "QModelIndex" }
            Parameter { name: "row"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationColumn"; type: "int" }
        }
        Signal {
            name: "columnsMoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
            Parameter { name: "destination"; type: "QModelIndex" }
            Parameter { name: "column"; type: "int" }
        }
        Method { name: "submit"; type: "bool" }
        Method { name: "revert" }
        Method {
            name: "hasIndex"
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "hasIndex"
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "parent"
            type: "QModelIndex"
            Parameter { name: "child"; type: "QModelIndex" }
        }
        Method {
            name: "sibling"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "idx"; type: "QModelIndex" }
        }
        Method {
            name: "rowCount"
            type: "int"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "rowCount"; type: "int" }
        Method {
            name: "columnCount"
            type: "int"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "columnCount"; type: "int" }
        Method {
            name: "hasChildren"
            type: "bool"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "hasChildren"; type: "bool" }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
        }
        Method {
            name: "fetchMore"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "canFetchMore"
            type: "bool"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "flags"
            type: "Qt::ItemFlags"
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
            Parameter { name: "flags"; type: "Qt::MatchFlags" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component { name: "QAbstractListModel"; prototype: "QAbstractItemModel" }
    Component {
        name: "QQuickCalendar"
        prototype: "QObject"
        exports: ["Qt.labs.calendar/Calendar 1.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Month"
            values: {
                "January": 0,
                "February": 1,
                "March": 2,
                "April": 3,
                "May": 4,
                "June": 5,
                "July": 6,
                "August": 7,
                "September": 8,
                "October": 9,
                "November": 10,
                "December": 11
            }
        }
    }
    Component {
        name: "QQuickCalendarModel"
        prototype: "QAbstractListModel"
        exports: ["Qt.labs.calendar/CalendarModel 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "from"; type: "QDate" }
        Property { name: "to"; type: "QDate" }
        Property { name: "count"; type: "int"; isReadonly: true }
        Method {
            name: "monthAt"
            type: "int"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "yearAt"
            type: "int"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "indexOf"
            type: "int"
            Parameter { name: "date"; type: "QDate" }
        }
        Method {
            name: "indexOf"
            type: "int"
            Parameter { name: "year"; type: "int" }
            Parameter { name: "month"; type: "int" }
        }
    }
    Component {
        name: "QQuickControl"
        defaultProperty: "data"
        prototype: "QQuickItem"
        Property { name: "font"; type: "QFont" }
        Property { name: "availableWidth"; type: "double"; isReadonly: true }
        Property { name: "availableHeight"; type: "double"; isReadonly: true }
        Property { name: "padding"; type: "double" }
        Property { name: "topPadding"; type: "double" }
        Property { name: "leftPadding"; type: "double" }
        Property { name: "rightPadding"; type: "double" }
        Property { name: "bottomPadding"; type: "double" }
        Property { name: "spacing"; type: "double" }
        Property { name: "locale"; type: "QLocale" }
        Property { name: "mirrored"; type: "bool"; isReadonly: true }
        Property { name: "focusPolicy"; type: "Qt::FocusPolicy" }
        Property { name: "focusReason"; type: "Qt::FocusReason" }
        Property { name: "visualFocus"; type: "bool"; isReadonly: true }
        Property { name: "hovered"; type: "bool"; isReadonly: true }
        Property { name: "hoverEnabled"; type: "bool" }
        Property { name: "wheelEnabled"; type: "bool" }
        Property { name: "background"; type: "QQuickItem"; isPointer: true }
        Property { name: "contentItem"; type: "QQuickItem"; isPointer: true }
        Property { name: "baselineOffset"; type: "double" }
        Property { name: "palette"; revision: 3; type: "QPalette" }
        Property { name: "horizontalPadding"; revision: 5; type: "double" }
        Property { name: "verticalPadding"; revision: 5; type: "double" }
        Property { name: "implicitContentWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitContentHeight"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitBackgroundWidth"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "implicitBackgroundHeight"; revision: 5; type: "double"; isReadonly: true }
        Property { name: "topInset"; revision: 5; type: "double" }
        Property { name: "leftInset"; revision: 5; type: "double" }
        Property { name: "rightInset"; revision: 5; type: "double" }
        Property { name: "bottomInset"; revision: 5; type: "double" }
        Signal { name: "paletteChanged"; revision: 3 }
        Signal { name: "horizontalPaddingChanged"; revision: 5 }
        Signal { name: "verticalPaddingChanged"; revision: 5 }
        Signal { name: "implicitContentWidthChanged"; revision: 5 }
        Signal { name: "implicitContentHeightChanged"; revision: 5 }
        Signal { name: "implicitBackgroundWidthChanged"; revision: 5 }
        Signal { name: "implicitBackgroundHeightChanged"; revision: 5 }
        Signal { name: "topInsetChanged"; revision: 5 }
        Signal { name: "leftInsetChanged"; revision: 5 }
        Signal { name: "rightInsetChanged"; revision: 5 }
        Signal { name: "bottomInsetChanged"; revision: 5 }
    }
    Component {
        name: "QQuickDayOfWeekRow"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: ["Qt.labs.calendar/AbstractDayOfWeekRow 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "source"; type: "QVariant" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
    }
    Component {
        name: "QQuickMonthGrid"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: ["Qt.labs.calendar/AbstractMonthGrid 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "month"; type: "int" }
        Property { name: "year"; type: "int" }
        Property { name: "source"; type: "QVariant" }
        Property { name: "title"; type: "string" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Signal {
            name: "pressed"
            Parameter { name: "date"; type: "QDate" }
        }
        Signal {
            name: "released"
            Parameter { name: "date"; type: "QDate" }
        }
        Signal {
            name: "clicked"
            Parameter { name: "date"; type: "QDate" }
        }
        Signal {
            name: "pressAndHold"
            Parameter { name: "date"; type: "QDate" }
        }
    }
    Component {
        name: "QQuickWeekNumberColumn"
        defaultProperty: "data"
        prototype: "QQuickControl"
        exports: ["Qt.labs.calendar/AbstractWeekNumberColumn 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "month"; type: "int" }
        Property { name: "year"; type: "int" }
        Property { name: "source"; type: "QVariant" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
    }
}
