import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable Qt.labs.location 1.0'

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        name: "LocationLabsSingleton"
        prototype: "QObject"
        exports: ["Qt.labs.location/QtLocationLabs 1.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [0]
        Method {
            name: "mapObjectsAt"
            type: "QList<QObject*>"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
            Parameter { name: "map"; type: "QDeclarativeGeoMap"; isPointer: true }
        }
    }
    Component {
        name: "QAbstractNavigator"
        prototype: "QObject"
        Signal {
            name: "activeChanged"
            Parameter { name: "active"; type: "bool" }
        }
        Signal {
            name: "waypointReached"
            Parameter { name: "pos"; type: "const QDeclarativeGeoWaypoint"; isPointer: true }
        }
        Signal { name: "destinationReached" }
        Signal { name: "currentRouteChanged" }
        Signal { name: "currentRouteLegChanged" }
        Signal { name: "currentSegmentChanged" }
        Signal { name: "nextManeuverIconChanged" }
        Signal { name: "progressInformationChanged" }
        Signal { name: "isOnRouteChanged" }
        Signal { name: "alternativeRoutesChanged" }
        Method { name: "start"; type: "bool" }
        Method { name: "stop"; type: "bool" }
        Method {
            name: "setTrackPosition"
            Parameter { name: "trackPosition"; type: "bool" }
        }
    }
    Component {
        name: "QDeclarativeNavigationBasicDirections"
        prototype: "QObject"
        Property { name: "nextManeuverIcon"; type: "QVariant"; isReadonly: true }
        Property { name: "distanceToNextManeuver"; type: "double"; isReadonly: true }
        Property { name: "remainingTravelDistance"; type: "double"; isReadonly: true }
        Property { name: "remainingTravelDistanceToNextWaypoint"; type: "double"; isReadonly: true }
        Property { name: "traveledDistance"; type: "double"; isReadonly: true }
        Property { name: "timeToNextManeuver"; type: "int"; isReadonly: true }
        Property { name: "remainingTravelTime"; type: "int"; isReadonly: true }
        Property { name: "remainingTravelTimeToNextWaypoint"; type: "int"; isReadonly: true }
        Property { name: "traveledTime"; type: "int"; isReadonly: true }
        Property { name: "currentRoute"; type: "QDeclarativeGeoRoute"; isReadonly: true; isPointer: true }
        Property {
            name: "currentRouteLeg"
            type: "QDeclarativeGeoRouteLeg"
            isReadonly: true
            isPointer: true
        }
        Property { name: "currentSegment"; type: "int"; isReadonly: true }
        Property {
            name: "alternativeRoutes"
            type: "QAbstractItemModel"
            isReadonly: true
            isPointer: true
        }
        Signal { name: "progressInformationChanged" }
        Signal {
            name: "waypointReached"
            Parameter { name: "pos"; type: "const QDeclarativeGeoWaypoint"; isPointer: true }
        }
        Signal { name: "destinationReached" }
    }
    Component {
        name: "QDeclarativeNavigator"
        defaultProperty: "quickChildren"
        prototype: "QParameterizableObject"
        exports: ["Qt.labs.location/Navigator 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "plugin"; type: "QDeclarativeGeoServiceProvider"; isPointer: true }
        Property { name: "map"; type: "QDeclarativeGeoMap"; isPointer: true }
        Property { name: "route"; type: "QDeclarativeGeoRoute"; isPointer: true }
        Property { name: "positionSource"; type: "QDeclarativePositionSource"; isPointer: true }
        Property { name: "active"; type: "bool" }
        Property { name: "navigatorReady"; type: "bool"; isReadonly: true }
        Property { name: "trackPositionSource"; type: "bool" }
        Property { name: "automaticReroutingEnabled"; type: "bool" }
        Property { name: "isOnRoute"; type: "bool"; isReadonly: true }
        Property {
            name: "directions"
            type: "QDeclarativeNavigationBasicDirections"
            isReadonly: true
            isPointer: true
        }
        Property { name: "error"; type: "NavigationError"; isReadonly: true }
        Property { name: "errorString"; type: "string"; isReadonly: true }
        Property { name: "engineHandle"; type: "QAbstractNavigator"; isReadonly: true; isPointer: true }
        Signal {
            name: "navigatorReadyChanged"
            Parameter { name: "ready"; type: "bool" }
        }
        Signal {
            name: "trackPositionSourceChanged"
            Parameter { name: "trackPositionSource"; type: "bool" }
        }
        Signal {
            name: "activeChanged"
            Parameter { name: "active"; type: "bool" }
        }
        Method { name: "recalculateRoutes" }
    }
    Component {
        name: "QGeoMapObject"
        defaultProperty: "quickChildren"
        prototype: "QParameterizableObject"
        Enum {
            name: "Type"
            values: {
                "InvalidType": 0,
                "ViewType": 1,
                "RouteType": 2,
                "RectangleType": 3,
                "CircleType": 4,
                "PolylineType": 5,
                "PolygonType": 6,
                "IconType": 7,
                "UserType": 256
            }
        }
        Property { name: "visible"; type: "bool" }
        Property { name: "type"; type: "Type"; isReadonly: true }
        Property { name: "geoShape"; type: "QGeoShape" }
        Signal { name: "selected" }
        Signal { name: "completed" }
    }
    Component {
        name: "QMapCircleObject"
        defaultProperty: "quickChildren"
        prototype: "QGeoMapObject"
        exports: ["Qt.labs.location/MapCircleObject 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "center"; type: "QGeoCoordinate" }
        Property { name: "radius"; type: "double" }
        Property { name: "color"; type: "QColor" }
        Property {
            name: "border"
            type: "QDeclarativeMapLineProperties"
            isReadonly: true
            isPointer: true
        }
    }
    Component {
        name: "QMapIconObject"
        defaultProperty: "quickChildren"
        prototype: "QGeoMapObject"
        exports: ["Qt.labs.location/MapIconObject 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "coordinate"; type: "QGeoCoordinate" }
        Property { name: "content"; type: "QVariant" }
        Property { name: "iconSize"; type: "QSizeF" }
        Signal {
            name: "contentChanged"
            Parameter { name: "content"; type: "QVariant" }
        }
        Signal {
            name: "coordinateChanged"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
    }
    Component {
        name: "QMapObjectView"
        defaultProperty: "quickChildren"
        prototype: "QGeoMapObject"
        exports: ["Qt.labs.location/MapObjectView 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "model"; type: "QVariant" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Signal {
            name: "modelChanged"
            Parameter { name: "model"; type: "QVariant" }
        }
        Signal {
            name: "delegateChanged"
            Parameter { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        }
        Method {
            name: "addMapObject"
            Parameter { name: "object"; type: "QGeoMapObject"; isPointer: true }
        }
        Method {
            name: "removeMapObject"
            Parameter { name: "object"; type: "QGeoMapObject"; isPointer: true }
        }
    }
    Component {
        name: "QMapPolygonObject"
        defaultProperty: "quickChildren"
        prototype: "QGeoMapObject"
        exports: ["Qt.labs.location/MapPolygonObject 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "path"; type: "QVariantList" }
        Property { name: "color"; type: "QColor" }
        Property {
            name: "border"
            type: "QDeclarativeMapLineProperties"
            isReadonly: true
            isPointer: true
        }
    }
    Component {
        name: "QMapPolylineObject"
        defaultProperty: "quickChildren"
        prototype: "QGeoMapObject"
        exports: ["Qt.labs.location/MapPolylineObject 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "path"; type: "QVariantList" }
        Property {
            name: "line"
            type: "QDeclarativeMapLineProperties"
            isReadonly: true
            isPointer: true
        }
    }
    Component {
        name: "QMapRouteObject"
        defaultProperty: "quickChildren"
        prototype: "QGeoMapObject"
        exports: ["Qt.labs.location/MapRouteObject 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "route"; type: "QDeclarativeGeoRoute"; isPointer: true }
        Signal {
            name: "routeChanged"
            Parameter { name: "route"; type: "QDeclarativeGeoRoute"; isPointer: true }
        }
    }
    Component {
        name: "QParameterizableObject"
        defaultProperty: "quickChildren"
        prototype: "QObject"
        Property { name: "quickChildren"; type: "QObject"; isList: true; isReadonly: true }
    }
}
