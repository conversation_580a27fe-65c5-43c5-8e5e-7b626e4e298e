#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف طباعة تقرير الحراسة (guard_report_printer.py)
مخصص لطباعة تقارير الأقسام والمستويات للحراسات
بنفس تصميم print6.py
"""

import os
from datetime import datetime
import sqlite3
from database_config import get_database_path, get_database_connection
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import cm
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
import sys
import traceback
import webbrowser
import subprocess
from PyQt5.QtWidgets import QMessageBox

# تسجيل الخطوط العربية (نفس الكود من print6.py)
try:
    pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))
    print("تم تسجيل خط Arabic (arial.ttf) بنجاح")
except Exception as font_error:
    print(f"خطأ في تسجيل خط Arabic (arial.ttf): {font_error}")
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        arial_path = os.path.join(script_dir, "arial.ttf")
        if os.path.exists(arial_path):
            pdfmetrics.registerFont(TTFont("Arabic", arial_path))
            print(f"تم تسجيل خط Arabic من المسار المطلق: {arial_path}")
        else:
            print(f"خطأ: ملف الخط غير موجود في المسار: {arial_path}")
    except Exception as alt_font_error:
        print(f"خطأ في تسجيل خط Arabic من المسار المطلق: {alt_font_error}")

# محاولة تسجيل خطوط Calibri
try:
    script_dir = os.path.dirname(os.path.abspath(__file__))
    calibri_regular_path = os.path.join(script_dir, "calibri.ttf")
    calibri_bold_path = os.path.join(script_dir, "calibrib.ttf")

    # البحث عن خطوط Calibri في مجلد الخطوط في Windows
    if os.name == 'nt':  # Windows
        windows_fonts_dir = os.path.join(os.environ.get('SystemRoot', 'C:\\Windows'), 'Fonts')
        if not os.path.exists(calibri_regular_path):
            windows_calibri_path = os.path.join(windows_fonts_dir, 'calibri.ttf')
            if os.path.exists(windows_calibri_path):
                calibri_regular_path = windows_calibri_path

        if not os.path.exists(calibri_bold_path):
            windows_calibri_bold_path = os.path.join(windows_fonts_dir, 'calibrib.ttf')
            if os.path.exists(windows_calibri_bold_path):
                calibri_bold_path = windows_calibri_bold_path

    # تسجيل خطوط Calibri
    if os.path.exists(calibri_regular_path):
        pdfmetrics.registerFont(TTFont("Calibri", calibri_regular_path))
        print(f"تم تسجيل خط Calibri من المسار: {calibri_regular_path}")

    if os.path.exists(calibri_bold_path):
        pdfmetrics.registerFont(TTFont("Calibri-Bold", calibri_bold_path))
        print(f"تم تسجيل خط Calibri-Bold من المسار: {calibri_bold_path}")

except Exception as font_error:
    print(f"خطأ في تسجيل الخطوط: {font_error}")

# Arabic text handling
import arabic_reshaper
from bidi.algorithm import get_display

def open_pdf(filename):
    """فتح ملف PDF باستخدام العارض الافتراضي"""
    try:
        absolute_path = os.path.abspath(filename)
        print(f"محاولة فتح الملف: {absolute_path}")

        try:
            if sys.platform == "win32":
                os.startfile(absolute_path)
            elif sys.platform == "darwin": # macOS
                os.system(f'open "{absolute_path}"')
            else: # Linux and other Unix-like
                os.system(f'xdg-open "{absolute_path}"')
        except OSError as e:
            if hasattr(e, 'winerror') and e.winerror == 1155:
                print(f"خطأ فتح الملف: لا يوجد تطبيق مرتبط لفتح ملفات PDF.")
                QMessageBox.warning(
                    None,
                    "فشل فتح الملف",
                    f"تم إنشاء ملف PDF بنجاح في:\n{absolute_path}\n\n"
                    "ولكن، لم يتم العثور على تطبيق افتراضي لفتح ملفات PDF.\n"
                    "الرجاء فتح الملف يدوياً."
                )
            else:
                print(f"خطأ غير متوقع في فتح الملف: {e}")
                traceback.print_exc()
        except Exception as open_error:
            print(f"خطأ عام في فتح الملف: {open_error}")
            traceback.print_exc()

    except Exception as path_error:
        print(f"خطأ في تحديد مسار الملف: {path_error}")
        traceback.print_exc()

def fix_arabic(text):
    """إصلاح النص العربي للعرض في PDF"""
    if not text:
        return ""
    try:
        reshaped_text = arabic_reshaper.reshape(str(text))
        return get_display(reshaped_text)
    except Exception as e:
        print(f"خطأ في إعادة تشكيل النص: {e}")
        return str(text)

def get_institution_info(db_path=None):
    """الحصول على معلومات المؤسسة من قاعدة البيانات"""
    try:
        if db_path is None:
            db_path = get_database_path()

        if not os.path.exists(db_path):
            print(f"تحذير: ملف قاعدة البيانات غير موجود: {db_path}")
            return {}

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT المؤسسة, السنة_الدراسية, ImagePath1, الحارس_العام FROM بيانات_المؤسسة LIMIT 1")
            institution_row = cursor.fetchone()
            if institution_row:
                institution_data = {
                    'name': institution_row[0],
                    'school_year': institution_row[1],
                    'logo_path': institution_row[2] if institution_row[2] and os.path.exists(institution_row[2]) else None,
                    'supervisor': institution_row[3] if len(institution_row) > 3 else ""
                }
                return institution_data
        except Exception as e:
            print(f"خطأ في قراءة بيانات المؤسسة: {e}")

        conn.close()
    except Exception as db_error:
        print(f"خطأ في الاتصال بقاعدة البيانات: {db_error}")

    return {}

def print_guard_sections_report(year, guard, db_path=None):
    """طباعة تقرير الأقسام والمستويات للحراسة المحددة"""
    try:
        # تنظيف اسم الملف من الرموز غير المسموحة
        safe_guard = guard.replace(" ", "_").replace("/", "-").replace("\\", "-").replace(":", "-")
        safe_year = year.replace("/", "-").replace("\\", "-").replace(":", "-")

        # حفظ الملف في المجلد المؤقت
        file_name = f"تقرير_{safe_guard}_{safe_year}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        temp_dir = os.path.join(os.path.dirname(__file__), "temp")

        # التأكد من إنشاء المجلد المؤقت
        try:
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
                print(f"تم إنشاء المجلد المؤقت: {temp_dir}")
        except Exception as dir_error:
            print(f"خطأ في إنشاء المجلد المؤقت: {dir_error}")
            # استخدام مجلد سطح المكتب كبديل
            temp_dir = os.path.join(os.path.expanduser("~"), "Desktop")

        file_path = os.path.join(temp_dir, file_name)
        print(f"مسار الملف المقترح: {file_path}")

        # الحصول على معلومات المؤسسة
        institution_data = get_institution_info(db_path)

        # جمع بيانات الأقسام المسندة للحراسة
        if db_path is None:
            db_path = get_database_path()
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # جلب الأقسام المسندة مع المستويات
        cursor.execute("""
            SELECT المستوى, القسم, مجموع_التلاميذ
            FROM البنية_التربوية 
            WHERE الأقسام_المسندة = ? AND السنة_الدراسية = ?
            ORDER BY ترتيب_المستويات, المستوى, القسم
        """, (guard, year))
        sections_data = cursor.fetchall()
        conn.close()
        
        if not sections_data:
            QMessageBox.warning(None, "تحذير", "لا توجد أقسام مسندة لهذه الحراسة")
            return False

        # تنظيم البيانات حسب المستوى
        levels_dict = {}
        total_students = 0
        
        for level, section, students_count in sections_data:
            if level not in levels_dict:
                levels_dict[level] = []
            levels_dict[level].append({
                'section': section,
                'students_count': students_count or 0
            })
            total_students += students_count or 0

        # إنشاء مستند PDF
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,
            rightMargin=1.2*cm,
            leftMargin=1.2*cm,
            topMargin=0.2*cm,
            bottomMargin=0.2*cm
        )

        # قائمة العناصر التي سيتم إضافتها للمستند
        elements = []

        # إنشاء أنماط الفقرات (نفس الأنماط من print6.py)
        styles = getSampleStyleSheet()

        # إنشاء نمط للنص العربي
        arabic_style = ParagraphStyle(
            'ArabicStyle',
            parent=styles['Normal'],
            fontName='Calibri',
            fontSize=14,
            textColor=colors.black,
            alignment=2,  # يمين
            leading=16,
            rightIndent=0,
            leftIndent=0
        )

        # إنشاء نمط للموضوع - محاذاة للوسط
        subject_style = ParagraphStyle(
            'SubjectStyle',
            parent=styles['Normal'],
            fontName='Calibri-Bold',
            fontSize=17,
            textColor=colors.black,
            alignment=1,  # وسط
            leading=19,
            rightIndent=0,
            leftIndent=0
        )

        # إنشاء نمط للعناوين الفرعية
        subtitle_style = ParagraphStyle(
            'SubtitleStyle',
            parent=styles['Heading2'],
            fontName='Calibri',
            fontSize=14,
            textColor=colors.darkblue,
            alignment=1,  # وسط
            spaceAfter=0.1*cm
        )

        # إنشاء نمط لاسم المؤسسة
        institution_style = ParagraphStyle(
            'InstitutionStyle',
            parent=styles['Title'],
            fontName='Calibri-Bold',
            fontSize=17,
            textColor=colors.darkblue,
            alignment=1,  # وسط
            spaceAfter=0.1*cm
        )

        # إضافة الشعار
        logo_path = institution_data.get('logo_path')
        if logo_path and os.path.exists(logo_path):
            try:
                img = Image(logo_path, width=200, height=90)
                img.hAlign = 'CENTER'
                elements.append(img)
                elements.append(Spacer(1, 0.5*cm))

                print(f"تم إضافة الشعار بنجاح من: {logo_path}")
            except Exception as logo_error:
                print(f"خطأ في إضافة الشعار: {logo_error}")

        # إضافة اسم المؤسسة
        institution_name = institution_data.get('name', '')
        if institution_name:
            elements.append(Paragraph(fix_arabic(institution_name), institution_style))
            elements.append(Spacer(1, 0.1*cm))

        # إضافة السنة الدراسية
        school_year = institution_data.get('school_year', '')
        if school_year:
            elements.append(Paragraph(fix_arabic(f"السنة الدراسية {school_year}"), subtitle_style))
            elements.append(Spacer(1, 0.1*cm))

        # إضافة عنوان التقرير
        elements.append(Paragraph(fix_arabic(f"الأقسام المسندة لـ{guard}"), subject_style))
        elements.append(Spacer(1, 0.5*cm))

        # تحديد عرض موحد للجداول
        total_width = 17.5*cm
        col_widths = [3*cm, 4*cm, 9*cm, 1.5*cm]  # إجمالي التلاميذ، القسم، المستوى، ر.ت

        # إضافة جدول المستويات
        if levels_dict:
            # إنشاء عنوان جدول المستويات
            levels_header_data = [[Paragraph(fix_arabic("ملخص المستويات"), subtitle_style)]]
            levels_header_table = Table(levels_header_data, colWidths=[total_width], hAlign='RIGHT')
            levels_header_table.setStyle(TableStyle([
                ('GRID', (0, 0), (-1, -1), 1, colors.darkblue),
                ('BACKGROUND', (0, 0), (0, 0), colors.lightblue),
                ('TEXTCOLOR', (0, 0), (0, 0), colors.darkblue),
                ('ALIGN', (0, 0), (0, 0), 'CENTER'),
                ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),
                ('LEFTPADDING', (0, 0), (0, 0), 6),
                ('RIGHTPADDING', (0, 0), (0, 0), 6),
                ('TOPPADDING', (0, 0), (0, 0), 6),
                ('BOTTOMPADDING', (0, 0), (0, 0), 6),
            ]))

            # إنشاء بيانات جدول المستويات
            levels_data = [
                [fix_arabic("إجمالي التلاميذ"), fix_arabic("عدد الأقسام"), fix_arabic("المستوى"), fix_arabic("ر.ت")]
            ]

            row_number = 1
            for level, sections in levels_dict.items():
                level_students = sum(s['students_count'] for s in sections)
                levels_data.append([
                    fix_arabic(str(level_students)),
                    fix_arabic(str(len(sections))),
                    fix_arabic(level),
                    fix_arabic(str(row_number))
                ])
                row_number += 1

            # إنشاء جدول المستويات
            levels_table = Table(levels_data, colWidths=col_widths, hAlign='RIGHT')
            levels_table.setStyle(TableStyle([
                ('GRID', (0, 0), (-1, -1), 1, colors.darkblue),
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('FONTNAME', (0, 0), (-1, 0), 'Calibri-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Calibri'),
                ('FONTSIZE', (0, 0), (-1, -1), 12),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))

            elements.append(levels_header_table)
            elements.append(levels_table)
            elements.append(Spacer(1, 0.3*cm))

        # إضافة جدول الأقسام التفصيلي
        # إنشاء عنوان جدول الأقسام
        sections_header_data = [[Paragraph(fix_arabic("تفاصيل الأقسام"), subtitle_style)]]
        sections_header_table = Table(sections_header_data, colWidths=[total_width], hAlign='RIGHT')
        sections_header_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.darkblue),
            ('BACKGROUND', (0, 0), (0, 0), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (0, 0), colors.darkblue),
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),
            ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (0, 0), 6),
            ('RIGHTPADDING', (0, 0), (0, 0), 6),
            ('TOPPADDING', (0, 0), (0, 0), 6),
            ('BOTTOMPADDING', (0, 0), (0, 0), 6),
        ]))

        # إنشاء بيانات جدول الأقسام
        sections_table_data = [
            [fix_arabic("عدد التلاميذ"), fix_arabic("القسم"), fix_arabic("المستوى"), fix_arabic("ر.ت")]
        ]

        row_number = 1
        for level, sections in levels_dict.items():
            for section_info in sections:
                sections_table_data.append([
                    fix_arabic(str(section_info['students_count'])),
                    fix_arabic(section_info['section']),
                    fix_arabic(level),
                    fix_arabic(str(row_number))
                ])
                row_number += 1

        # إضافة صف المجموع
        sections_table_data.append([
            fix_arabic(str(total_students)),
            fix_arabic(f"المجموع: {len(sections_data)} قسم"),
            fix_arabic(""),
            fix_arabic("")
        ])

        # إنشاء جدول الأقسام
        sections_table = Table(sections_table_data, colWidths=col_widths, hAlign='RIGHT')
        sections_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.darkblue),
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Calibri-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Calibri'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            # تمييز صف المجموع
            ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
            ('FONTNAME', (0, -1), (-1, -1), 'Calibri-Bold'),
        ]))

        elements.append(sections_header_table)
        elements.append(sections_table)
        elements.append(Spacer(1, 0.2*cm))

        # إضافة التوقيع
        signature_data = [
            [fix_arabic("توقيع الحراسة العامة"), ""]
        ]

        signature_table = Table(signature_data, colWidths=[5*cm, 12.5*cm], hAlign='RIGHT')
        signature_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Calibri-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 13),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 10),
        ]))

        elements.append(signature_table)

        # بناء المستند
        doc.build(elements)

        # فتح الملف
        open_pdf(file_path)
        return True

    except Exception as e:
        print(f"خطأ في طباعة تقرير الحراسة: {str(e)}")
        traceback.print_exc()
        QMessageBox.critical(
            None,
            "خطأ طباعة",
            f"حدث خطأ أثناء إنشاء تقرير الحراسة:\n{e}"
        )
        return False
