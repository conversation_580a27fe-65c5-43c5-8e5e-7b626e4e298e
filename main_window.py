# main_window.py
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import datetime
import gc
import glob
import zipfile

# استيراد إعدادات قاعدة البيانات
from database_config import get_database_path, get_database_connection

# استيراد محسن الأداء
from performance_optimizer import optimize_app_performance, finalize_window

# إعداد QtWebEngine قبل إنشاء QApplication
from PyQt5.QtCore import QCoreApplication, Qt, QTimer, QSize
QCoreApplication.setAttribute(Qt.AA_ShareOpenGLContexts)

# تهيئة مبكرة لـ QtWebEngine
try:
    from PyQt5 import QtWebEngineWidgets
    print("INFO: تم تهيئة QtWebEngine بنجاح")
except ImportError:
    print("WARNING: QtWebEngine غير متوفر - ستستخدم النوافذ التقليدية")

from PyQt5.QtWidgets import (QApplication, Q<PERSON>ain<PERSON><PERSON>ow, QWidget, QPushButton, QVBoxLayout,
                             QHBoxLayout, QFrame, QDesktopWidget,
                             QStackedWidget, QMessageBox, QLabel, QSpacerItem, QSizePolicy,
                             QTableWidget, QTabWidget, QTabBar, QDialog, QStyle)
from PyQt5.QtGui import QFont, QIcon, QColor, QPixmap
from PyQt5.QtCore import QTimer, QSize # type: ignore

# --- نظام التحكم في مستوى الرسائل ---
VERBOSE_MODE = False
SHOW_FONT_REGISTRATION = False
SHOW_CSS_WARNINGS = False

def debug_print(message, force=False):
    """طباعة الرسائل حسب مستوى التفصيل المحدد"""
    if VERBOSE_MODE or force:
        print(message)

def suppress_qt_warnings():
    """قمع تحذيرات Qt غير المهمة"""
    if not SHOW_CSS_WARNINGS:
        # قمع المزيد من التحذيرات لتقليل الومضات
        os.environ['QT_LOGGING_RULES'] = (
            '*.debug=false;'
            'qt.qpa.*=false;'
            'qt.webenginecontext.debug=false;'
            'qt.webengine.*=false;'
            'qt.webchannel.*=false;'
            'qt.network.ssl.debug=false;'
            'qt.quick.*=false'
        )

# تطبيق قمع التحذيرات
suppress_qt_warnings()

# تطبيق تحسينات الأداء على مستوى التطبيق
try:
    from performance_optimizer import PerformanceOptimizer
    PerformanceOptimizer.setup_qt_environment()
    PerformanceOptimizer.suppress_debug_messages()
except ImportError:
    pass

# إنشاء كومبوننت لشريط تبويب مزدوج الصف
class TwoRowTabComponent(QWidget):
    """مكون مخصص يستخدم صفين من QTabBar مع QStackedWidget للمحتوى"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("TwoRowTabComponent")

        # إنشاء التخطيط الرئيسي
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)

        # إنشاء إطار لشريط التبويب
        self.tab_frame = QFrame()
        self.tab_frame.setObjectName("TabFrame")
        self.tab_frame.setMinimumHeight(70)
       

        # تخطيط شريط التبويب
        self.tab_layout = QVBoxLayout(self.tab_frame)
        self.tab_layout.setContentsMargins(0, 0, 0, 0)
        self.tab_layout.setSpacing(5)

        # إنشاء شريط التبويب العلوي (أول 10 تبويبات)
        self.top_tab_bar = QTabBar()
        self.top_tab_bar.setObjectName("TopTabBar")
        self.top_tab_bar.setShape(QTabBar.RoundedNorth)
        self.top_tab_bar.setExpanding(False)
        self.top_tab_bar.setDrawBase(False)
        self.top_tab_bar.setCursor(Qt.PointingHandCursor)

        # إنشاء شريط التبويب السفلي (باقي التبويبات)
        self.bottom_tab_bar = QTabBar()
        self.bottom_tab_bar.setObjectName("BottomTabBar")
        self.bottom_tab_bar.setShape(QTabBar.RoundedNorth)
        self.bottom_tab_bar.setExpanding(False)
        self.bottom_tab_bar.setDrawBase(False)
        self.bottom_tab_bar.setCursor(Qt.PointingHandCursor)

        # إضافة أشرطة التبويب إلى التخطيط
        self.tab_layout.addWidget(self.top_tab_bar)
        self.tab_layout.addWidget(self.bottom_tab_bar)

        # إنشاء محتوى الويدجيت المكدس
        self.stacked_widget = QStackedWidget()
        self.stacked_widget.setObjectName("TabContent")

        # ربط إشارات تغيير التبويب بالدوال المعالجة
        self.top_tab_bar.currentChanged.connect(self._on_top_tab_changed)
        self.bottom_tab_bar.currentChanged.connect(self._on_bottom_tab_changed)

        # تخزين الإشارة الخارجية لتغيير التبويب
        self.current_changed_signal = None

        # إضافة المكونات إلى التخطيط الرئيسي
        self.layout.addWidget(self.tab_frame)
        self.layout.addWidget(self.stacked_widget, 1)

        # المتغيرات الداخلية
        self.first_row_count = 10
        self.previous_tab_index = 0
        self.tab_data = {}

    def _on_top_tab_changed(self, index):
        """معالجة تغيير تبويب في الصف العلوي"""
        if index < 0:  # إذا تم إلغاء تحديد جميع التبويبات
            return

        # الحصول على البيانات المرتبطة بالتبويب
        tab_data = self.top_tab_bar.tabData(index)

        if tab_data:
            # حفظ المؤشر السابق
            self.previous_tab_index = self.stacked_widget.currentIndex()

            # تعيين الويدجيت المكدس للتبويب المحدد
            self.stacked_widget.setCurrentIndex(index)

            # إلغاء تحديد جميع التبويبات في الصف السفلي
            self.bottom_tab_bar.blockSignals(True)
            self.bottom_tab_bar.setCurrentIndex(-1)
            self.bottom_tab_bar.blockSignals(False)

            # استدعاء الإشارة الخارجية إذا كانت موجودة
            if self.current_changed_signal:
                self.current_changed_signal(index, tab_data)

    def _on_bottom_tab_changed(self, index):
        """معالجة تغيير تبويب في الصف السفلي"""
        if index < 0:  # إذا تم إلغاء تحديد جميع التبويبات
            return

        # حساب المؤشر الفعلي (مع مراعاة التبويبات في الصف العلوي)
        actual_index = index + self.first_row_count

        # الحصول على البيانات المرتبطة بالتبويب
        tab_data = self.bottom_tab_bar.tabData(index)

        if tab_data:
            # حفظ المؤشر السابق
            self.previous_tab_index = self.stacked_widget.currentIndex()

            # تعيين الويدجيت المكدس للتبويب المحدد
            self.stacked_widget.setCurrentIndex(actual_index)

            # إلغاء تحديد جميع التبويبات في الصف العلوي
            self.top_tab_bar.blockSignals(True)
            self.top_tab_bar.setCurrentIndex(-1)
            self.top_tab_bar.blockSignals(False)

            # استدعاء الإشارة الخارجية إذا كانت موجودة
            if self.current_changed_signal:
                self.current_changed_signal(actual_index, tab_data)

    def addTab(self, page, text):
        """إضافة تبويب جديد وصفحة مرتبطة به"""
        # إضافة الصفحة إلى الويدجيت المكدس
        index = self.stacked_widget.addWidget(page)

        # تحديد أي صف سيتم إضافة التبويب إليه
        if index < self.first_row_count:
            # إضافة التبويب للصف العلوي
            tab_index = self.top_tab_bar.addTab(text)
            self.top_tab_bar.setTabData(tab_index, index)
        else:
            # إضافة التبويب للصف السفلي
            tab_index = self.bottom_tab_bar.addTab(text)
            self.bottom_tab_bar.setTabData(tab_index, index)

        return index

    def setTabData(self, index, data):
        """تعيين بيانات مخصصة للتبويب المحدد"""
        if index < self.first_row_count:
            self.top_tab_bar.setTabData(index, data)
        else:
            self.bottom_tab_bar.setTabData(index - self.first_row_count, data)

    def tabData(self, index):
        """استرجاع بيانات التبويب المحدد"""
        if index < self.first_row_count:
            return self.top_tab_bar.tabData(index)
        else:
            return self.bottom_tab_bar.tabData(index - self.first_row_count)

    def setCurrentIndex(self, index):
        """تعيين التبويب الحالي"""
        if index < 0 or index >= self.stacked_widget.count():
            return

        # إلغاء تحديد جميع التبويبات في كلا الصفين أولاً
        self.top_tab_bar.blockSignals(True)
        self.bottom_tab_bar.blockSignals(True)
        
        # إلغاء تحديد جميع التبويبات
        self.top_tab_bar.setCurrentIndex(-1)
        self.bottom_tab_bar.setCurrentIndex(-1)

        # تحديد الصف المناسب وتعيين التبويب النشط
        if index < self.first_row_count:
            self.top_tab_bar.setCurrentIndex(index)
        else:
            bottom_index = index - self.first_row_count
            self.bottom_tab_bar.setCurrentIndex(bottom_index)

        # إعادة تفعيل الإشارات
        self.top_tab_bar.blockSignals(False)
        self.bottom_tab_bar.blockSignals(False)

        # تعيين المحتوى المناسب
        self.stacked_widget.setCurrentIndex(index)

    def currentIndex(self):
        """الحصول على مؤشر التبويب الحالي"""
        return self.stacked_widget.currentIndex()

    def setTabEnabled(self, index, enabled):
        """تمكين أو تعطيل تبويب محدد"""
        if index < self.first_row_count:
            self.top_tab_bar.setTabEnabled(index, enabled)
        else:
            self.bottom_tab_bar.setTabEnabled(index - self.first_row_count, enabled)

    def setTabToolTip(self, index, tooltip):
        """تعيين تلميح أداة للتبويب المحدد"""
        if index < self.first_row_count:
            self.top_tab_bar.setTabToolTip(index, tooltip)
        else:
            self.bottom_tab_bar.setTabToolTip(index - self.first_row_count, tooltip)

    def count(self):
        """عدد التبويبات الإجمالي"""
        return self.stacked_widget.count()

    def blockSignals(self, block):
        """تعطيل أو تمكين الإشارات لجميع المكونات"""
        self.top_tab_bar.blockSignals(block)
        self.bottom_tab_bar.blockSignals(block)
        return super().blockSignals(block)

    def setStyleSheet(self, sheet):
        """تعيين ورقة الأنماط مع تنظيف الخصائص غير المدعومة"""
        # إزالة الخصائص غير المدعومة في PyQt5
        cleaned_sheet = sheet.replace('box-shadow:', '/* box-shadow:').replace('cursor:', '/* cursor:')
        super().setStyleSheet(cleaned_sheet)

    def connectCurrentChanged(self, slot):
        """ربط إشارة تغيير التبويب بدالة خارجية"""
        self.current_changed_signal = slot

# تعريف نافذة مؤقتة بسيطة
class PlaceholderWindow(QWidget):
    def __init__(self, title="مؤقت", message="المحتوى غير متوفر", db=None, academic_year=None, parent=None, **kwargs):
        super().__init__(parent)
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignCenter)
        self.label = QLabel(f"<h2>{title}</h2><p>{message}</p>")
        self.label.setAlignment(Qt.AlignCenter)
        self.label.setFont(QFont("Calibri", 14))
        self.label.setWordWrap(True)
        layout.addWidget(self.label)
        self.setWindowTitle(title)

class MainWindow(QMainWindow):
    def __init__(self, auto_show=False):
        super().__init__()

        # تطبيق تحسينات الأداء
        optimize_app_performance(self)

        self.setWindowTitle("")  # إزالة العنوان الرئيسي
        self.setLayoutDirection(Qt.RightToLeft)
        self.previous_tab_index = 0

        # تقليل رسائل التشخيص
        self.setup_logging()

        # تحديد مسار قاعدة البيانات باستخدام النظام الجديد
        # استخدام دالة get_database_path من database_config للحصول على المسار الصحيح
        try:
            self.db_path = get_database_path()
            debug_print(f"INFO: التطبيق غير محزم - مجلد الملفات: {os.path.dirname(os.path.abspath(__file__))}")
            debug_print(f"INFO: مسار قاعدة البيانات: {self.db_path}")
        except Exception as e:
            print(f"ERROR: خطأ في الحصول على مسار قاعدة البيانات: {e}")
            # fallback للنظام القديم
            if getattr(sys, 'frozen', False):
                self.db_folder = os.path.dirname(sys.executable)
                print(f"INFO: البرنامج محزم - مجلد البرنامج: {self.db_folder}")
            else:
                self.db_folder = os.path.dirname(os.path.abspath(__file__))
                print(f"INFO: البرنامج من الكود المصدري - مجلد البرنامج: {self.db_folder}")
            self.db_path = os.path.join(self.db_folder, "data.db")

        # التحقق من وجود قاعدة البيانات - لا ننشئ قاعدة جديدة أبد
        try:
            import sqlite3

            # التحقق من وجود قاعدة البيانات
            if not os.path.exists(self.db_path):
                # إظهار رسالة خطأ للمستخدم
                error_msg = f"""
❌ خطأ: لم يتم العثور على قاعدة البيانات!

المسار المطلوب: {self.db_path}

يرجى التأكد من وجود ملف قاعدة البيانات في المكان الصحيح.
                """
                QMessageBox.critical(self, "خطأ في قاعدة البيانات", error_msg)
                print(f"ERROR: قاعدة البيانات غير موجودة: {self.db_path}")
                sys.exit(1)  # إنهاء البرنامج

            # إنشاء اتصال قاعدة البيانات للنوافذ التي تحتاجه
            self.db_connection = None  # تم تعديل هذا
            self.db = get_database_connection()  # استخدام الاتصال الجديد من database_config
            debug_print(f"INFO: تم الاتصال بقاعدة البيانات بنجاح: {self.db_path}")

            # إنشاء اتصال QSqlDatabase للنوافذ المتقدمة مثل sub4_window
            from PyQt5.QtSql import QSqlDatabase # type: ignore
            self.qsql_db = QSqlDatabase.addDatabase("QSQLITE", "main_connection")
            self.qsql_db.setDatabaseName(self.db_path)
            if self.qsql_db.open():
                print("INFO: تم إنشاء اتصال QSqlDatabase للنوافذ المتقدمة")
            else:
                print("WARNING: فشل في إنشاء اتصال QSqlDatabase")

            print(f"INFO: تم العثور على قاعدة البيانات: {self.db_path}")
            print("INFO: تم إنشاء اتصال قاعدة البيانات للنوافذ")
        except Exception as e:
            error_msg = f"خطأ في الوصول لقاعدة البيانات: {e}"
            QMessageBox.critical(self, "خطأ", error_msg)
            print(f"ERROR: {error_msg}")
            sys.exit(1)  # إنهاء البرنامج

        self.menuBar().setVisible(False)

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        self._create_top_navbar()
        self._setup_content_area()

        self.main_layout.addWidget(self.navbar_frame)
        self.main_layout.addWidget(self.content_area, 1)

        # استخراج السنة الدراسية من قاعدة البيانات
        self.current_academic_year = self.get_current_academic_year()

        # تحديث عنوان النافذة مع اسم المعين في الحراسة العامة
        self.get_guard_name_and_update_title()

        # إنشاء مراقب لتغييرات قاعدة البيانات
        self.setup_database_monitor()

        self._create_windows()
        self._link_navbar_buttons()

        # تحديد النافذة الافتراضية - تبسيط الكود المعقد
        if self.tabWidget.count() > 0 and self.content_area.count() > 0:
            self.tabWidget.setCurrentIndex(0)
            self.content_area.setCurrentIndex(0)
            self.previous_tab_index = 0
            print("INFO: تم تحديد النافذة الافتراضية.")

        # إنهاء تحسينات الأداء وإعادة تفعيل التحديثات
        finalize_window(self)

        # عرض النافذة فقط إذا كان مطلوب
        if auto_show:
            self.showMaximized()
            print("INFO: تم عرض النافذة الرئيسية (Maximized).")

        # متغيرات التحديث
        self.last_table_update_time = 0
        self.app_version = "1.0.0"

        # إضافة ملصق لعرض حالة التحميل
        self.loading_label = QLabel("جاري التحميل...", self)
        self.loading_label.setAlignment(Qt.AlignCenter)
        self.loading_label.setStyleSheet("background-color: rgba(0, 0, 0, 150); color: white; font-size: 16pt; padding: 20px; border-radius: 10px;")
        self.loading_label.hide()

    def setup_application_icon(self):
        """إعداد أيقونة التطبيق بطريقة محسنة للبرامج المحزمة"""
        try:
            # البحث عن الأيقونة في عدة مواقع محتملة
            icon_paths = [
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "01.ico"),  # في مجلد البرنامج
                "01.ico",  # في المجلد الحالي
                os.path.join(os.path.dirname(sys.executable), "01.ico") if getattr(sys, 'frozen', False) else None,
            ]

            # إزالة المسارات الفارغة
            icon_paths = [path for path in icon_paths if path is not None]

            app_icon = None
            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    app_icon = QIcon(icon_path)
                    print(f"INFO: تم العثور على الأيقونة في: {icon_path}")
                    break

            if app_icon and not app_icon.isNull():
                # تطبيق الأيقونة على النافذة الرئيسية
                self.setWindowIcon(app_icon)

                # تطبيق الأيقونة على التطبيق كاملاً (مهم للبرامج المحزمة)
                QApplication.instance().setWindowIcon(app_icon)

                # حفظ الأيقونة للاستخدام في النوافذ الأخرى
                self.app_icon = app_icon

                print("INFO: تم تطبيق الأيقونة بنجاح")
            else:
                print("WARNING: لم يتم العثور على ملف الأيقونة")
                self.app_icon = None

        except Exception as e:
            print(f"ERROR: خطأ في إعداد الأيقونة: {e}")
            self.app_icon = None

    def setup_logging(self):
        """إعداد نظام التسجيل لتقليل الرسائل المكررة"""
        self.logged_messages = set()
        
    def log_once(self, message, level="INFO"):
        """طباعة الرسالة مرة واحدة فقط"""
        if message not in self.logged_messages:
            self.logged_messages.add(message)
            if VERBOSE_MODE:
                print(f"{level}: {message}")

    def check_for_updates(self):
        """التحقق من وجود تحديثات للبرنامج - نسخة مبسطة"""
        pass  # تم تعطيل التحقق من التحديثات مؤقت

    def get_current_academic_year(self):
        """استخراج السنة الدراسية من جدول بيانات_المؤسسة"""
        try:
            # التحقق من وجود قاعدة البيانات
            if not os.path.exists(self.db_path):
                return "2024/2025"

            # الاتصال بقاعدة البيانات
            conn = get_database_connection()
            cursor = conn.cursor()

            # التحقق من وجود جدول بيانات_المؤسسة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
            if not cursor.fetchone():
                conn.close()
                return "2024/2025"

            # استخراج السنة الدراسية من الجدول
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة WHERE السنة_الدراسية IS NOT NULL AND السنة_الدراسية != '' ORDER BY rowid DESC LIMIT 1")
            result = cursor.fetchone()

            conn.close()

            if result and result[0]:
                academic_year = result[0].strip()
                if academic_year and academic_year != "اختر السنة الدراسية":
                    return academic_year

            # إذا لم توجد سنة دراسية صالحة، استخدام السنة الافتراضية
            return "2024/2025"

        except Exception as e:
            print(f"ERROR: خطأ في استخراج السنة الدراسية من قاعدة البيانات: {e}")
            return "2024/2025"

    def get_guard_name_and_update_title(self):
        """استخراج اسم المعين في الحراسة العامة وتحديث عنوان النافذة"""
        try:
            import sqlite3

            # التحقق من وجود قاعدة البيانات
            if not os.path.exists(self.db_path):
                self.setWindowTitle("برنامج المعين في الحراسة العامة")
                return

            # الاتصال بقاعدة البيانات
            conn = get_database_connection()
            cursor = conn.cursor()

            # التحقق من وجود جدول بيانات_المؤسسة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
            if not cursor.fetchone():
                conn.close()
                self.setWindowTitle("برنامج المعين في الحراسة العامة")
                return

            conn.close()

            # استخدام العنوان الثابت للبرنامج
            self.setWindowTitle("برنامج المعين في الحراسة العامة")

        except Exception as e:
            print(f"ERROR: خطأ في استخراج اسم المعين في الحراسة العامة: {e}")
            self.setWindowTitle("برنامج المعين في الحراسة العامة")

    def update_current_academic_year(self):
        """تحديث السنة الدراسية الحالية من قاعدة البيانات"""
        old_year = getattr(self, 'current_academic_year', None)
        new_year = self.get_current_academic_year()

        if old_year != new_year:
            self.current_academic_year = new_year
            print(f"INFO: تم تحديث السنة الدراسية من {old_year} إلى {new_year}")
            
            # تحديث عنوان النافذة مع اسم المعين في الحراسة العامة
            self.get_guard_name_and_update_title()

            # إشعار النوافذ الأخرى بتغيير السنة الدراسية
            self.notify_windows_academic_year_changed(new_year)

    def notify_windows_academic_year_changed(self, new_year):
        """إشعار النوافذ الأخرى بتغيير السنة الدراسية"""
        try:
            # تحديث النوافذ التي تحتاج للسنة الدراسية
            windows_to_update = [
                'sub4_window',  # اللوائح والأقسام
                'sub9_window__html',  # البحث
                'sub20_window',  # طباعة اللوائح
                'sub9_window'  # مسك الغياب
            ]

            for window_key in windows_to_update:
                if hasattr(self, window_key):
                    window = getattr(self, window_key)
                    if window and hasattr(window, 'update_academic_year'):
                        try:
                            window.update_academic_year(new_year)
                            print(f"INFO: تم تحديث السنة الدراسية في {window_key}")
                        except Exception as e:
                            print(f"WARNING: فشل تحديث السنة الدراسية في {window_key}: {e}")

        except Exception as e:
            print(f"ERROR: خطأ في إشعار النوافذ بتغيير السنة الدراسية: {e}")

    def on_academic_year_changed(self, new_year):
        """معالج إشارة تغيير السنة الدراسية من نافذة بيانات المؤسسة"""
        try:
            print(f"INFO: تم استقبال إشارة تغيير السنة الدراسية إلى: {new_year}")

            # تحديث السنة الدراسية في النافذة الرئيسية
            old_year = getattr(self, 'current_academic_year', None)
            self.current_academic_year = new_year

            # إشعار جميع النوافذ الفرعية بالتغيير
            self.notify_windows_academic_year_changed(new_year)

            # تحديث السنة في النوافذ المحددة
            self.update_specific_windows_academic_year(new_year)

            print(f"INFO: تم تحديث السنة الدراسية من {old_year} إلى {new_year} في جميع النوافذ")

        except Exception as e:
            print(f"ERROR: خطأ في معالجة تغيير السنة الدراسية: {e}")

    def update_specific_windows_academic_year(self, new_year):
        """تحديث النوافذ المحددة بالسنة الدراسية الجديدة"""
        try:
            # تحديث نافذة sub4_window (اللوائح والأقسام)
            if hasattr(self, 'sub4_window') and self.sub4_window:
                try:
                    if hasattr(self.sub4_window, 'update_academic_year'):
                        self.sub4_window.update_academic_year(new_year)
                    elif hasattr(self.sub4_window, 'current_academic_year'):
                        self.sub4_window.current_academic_year = new_year
                        if hasattr(self.sub4_window, 'load_academic_year_from_database'):
                            self.sub4_window.load_academic_year_from_database()
                        if hasattr(self.sub4_window, 'load_initial_data'):
                            self.sub4_window.load_initial_data()
                    print("INFO: تم تحديث نافذة sub4_window")
                except Exception as e:
                    print(f"WARNING: فشل تحديث نافذة sub4_window: {e}")

            # تحديث نافذة sub01_window (النافذة الرئيسية)
            if hasattr(self, 'sub01_window') and self.sub01_window:
                try:
                    if hasattr(self.sub01_window, 'update_academic_year'):
                        self.sub01_window.update_academic_year(new_year)
                    elif hasattr(self.sub01_window, 'current_academic_year'):
                        self.sub01_window.current_academic_year = new_year
                        # إعادة تحميل البيانات إذا كانت الدالة موجودة
                        if hasattr(self.sub01_window, 'refresh_data'):
                            self.sub01_window.refresh_data()
                    print("INFO: تم تحديث نافذة sub01_window")
                except Exception as e:
                    print(f"WARNING: فشل تحديث نافذة sub01_window: {e}")

            # تحديث النوافذ الأخرى
            other_windows = ['sub9_window__html', 'sub20_window', 'sub9_window']
            for window_name in other_windows:
                if hasattr(self, window_name):
                    window = getattr(self, window_name)
                    if window:
                        try:
                            if hasattr(window, 'update_academic_year'):
                                window.update_academic_year(new_year)
                            elif hasattr(window, 'current_academic_year'):
                                window.current_academic_year = new_year
                            print(f"INFO: تم تحديث نافذة {window_name}")
                        except Exception as e:
                            print(f"WARNING: فشل تحديث نافذة {window_name}: {e}")

        except Exception as e:
            print(f"ERROR: خطأ في تحديث النوافذ المحددة: {e}")

    def setup_database_monitor(self):
        """إعداد مراقب قاعدة البيانات لتتبع تغييرات السنة الدراسية"""
        try:
            from PyQt5.QtCore import QTimer

            # إنشاء مؤقت لمراقبة قاعدة البيانات كل 2 ثانية
            self.db_monitor_timer = QTimer()
            self.db_monitor_timer.timeout.connect(self.check_academic_year_changes)
            self.db_monitor_timer.start(2000)  # كل 2 ثانية

            # حفظ آخر سنة دراسية معروفة
            self.last_known_academic_year = self.current_academic_year

        except Exception as e:
            print(f"ERROR: خطأ في إعداد مراقب قاعدة البيانات: {e}")

    def check_academic_year_changes(self):
        """فحص تغييرات السنة الدراسية في قاعدة البيانات"""
        try:
            # الحصول على السنة الدراسية الحالية من قاعدة البيانات
            current_year = self.get_current_academic_year()

            # مقارنة مع آخر سنة معروفة
            if current_year != self.last_known_academic_year:
                print(f"INFO: تم اكتشاف تغيير في السنة الدراسية من {self.last_known_academic_year} إلى {current_year}")

                # تحديث السنة الدراسية
                self.current_academic_year = current_year
                self.last_known_academic_year = current_year
                
                # تحديث عنوان النافذة مع اسم المعين في الحراسة العامة
                self.get_guard_name_and_update_title()

                # تحديث جميع النوافذ فور
                self.force_update_all_windows(current_year)

        except Exception as e:
            print(f"ERROR: خطأ في فحص تغييرات السنة الدراسية: {e}")

    def force_update_all_windows(self, new_year):
        """إجبار تحديث جميع النوافذ بالسنة الدراسية الجديدة"""
        try:
            print(f"INFO: بدء التحديث الإجباري لجميع النوافذ بالسنة الدراسية: {new_year}")

            # قائمة النوافذ المطلوب تحديثها
            windows_to_update = [
                ('sub4_window', 'نافذة اللوائح والأقسام'),
                ('sub01_window', 'النافذة الرئيسية'),
                ('sub9_window__html', 'نافذة البحث'),
                ('sub20_window', 'نافذة طباعة اللوائح'),
                ('sub9_window', 'نافذة مسك الغياب')
            ]

            updated_count = 0

            for window_attr, window_name in windows_to_update:
                if hasattr(self, window_attr):
                    window = getattr(self, window_attr)
                    if window:
                        try:
                            # طريقة 1: استدعاء دالة update_academic_year إذا كانت موجودة
                            if hasattr(window, 'update_academic_year'):
                                window.update_academic_year(new_year)
                                print(f"✅ تم تحديث {window_name} باستخدام update_academic_year")
                                updated_count += 1
                                continue

                            # طريقة 2: تحديث المتغير مباشرة وإعادة تحميل البيانات
                            if hasattr(window, 'current_academic_year'):
                                window.current_academic_year = new_year

                                # محاولة إعادة تحميل البيانات
                                if hasattr(window, 'load_initial_data'):
                                    window.load_initial_data()
                                elif hasattr(window, 'load_data'):
                                    window.load_data()
                                elif hasattr(window, 'refresh_data'):
                                    window.refresh_data()
                                elif hasattr(window, 'update_data'):
                                    window.update_data()

                                print(f"✅ تم تحديث {window_name} بتحديث المتغير وإعادة التحميل")
                                updated_count += 1
                                continue

                            # طريقة 3: إعادة إنشاء النافذة إذا لزم الأمر
                            print(f"⚠️ {window_name} لا تحتوي على آلية تحديث مناسبة")

                        except Exception as e:
                            print(f"❌ فشل تحديث {window_name}: {e}")

            print(f"INFO: تم تحديث {updated_count} نافذة من أصل {len(windows_to_update)} نوافذ")

            # إشعار المستخدم بالتحديث
            if updated_count > 0:
                print(f"🎉 تم تحديث النوافذ بنجاح للسنة الدراسية: {new_year}")

        except Exception as e:
            print(f"ERROR: خطأ في التحديث الإجباري للنوافذ: {e}")

    def _reload_lists_sections_window(self):
        """إعادة تحميل نافذة اللوائح والأقسام من جديد"""
        try:
            print("INFO: بدء إعادة تحميل نافذة اللوائح والأقسام...")

            # الحصول على السنة الدراسية الحالية من قاعدة البيانات
            current_year = self.get_current_academic_year()
            self.current_academic_year = current_year

            # التحقق من وجود النافذة الحالية
            if hasattr(self, 'windows') and 'lists_sections' in self.windows:
                old_window = self.windows['lists_sections']

                # محاولة تحديث النافذة الموجودة أولاً
                if hasattr(old_window, 'update_academic_year'):
                    try:
                        old_window.update_academic_year(current_year)
                        print(f"SUCCESS: تم تحديث نافذة اللوائح والأقسام بالسنة الدراسية: {current_year}")
                        return
                    except Exception as e:
                        print(f"WARNING: فشل تحديث النافذة الموجودة: {e}")

                # إذا فشل التحديث، إعادة إنشاء النافذة
                print("INFO: إعادة إنشاء نافذة اللوائح والأقسام...")

                # إزالة النافذة القديمة من منطقة المحتوى
                if hasattr(self, 'content_area'):
                    self.content_area.removeWidget(old_window)

                # حذف النافذة القديمة
                old_window.deleteLater()

            # إنشاء نافذة جديدة
            try:
                from sub4_window import Sub4Window

                new_window = Sub4Window(
                    db=self.qsql_db,
                    academic_year=current_year,
                    parent=self
                )

                # تحويل النافذة إلى ويدجيت مدمج
                new_window.setWindowFlags(Qt.Widget)
                new_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

                # إخفاء شريط القوائم وشريط الحالة
                if hasattr(new_window, 'menuBar'):
                    new_window.menuBar().setVisible(False)
                if hasattr(new_window, 'statusBar'):
                    new_window.statusBar().setVisible(False)

                # تحديث قاموس النوافذ
                self.windows['lists_sections'] = new_window

                # إضافة النافذة الجديدة لمنطقة المحتوى
                if hasattr(self, 'content_area'):
                    self.content_area.addWidget(new_window)

                print(f"SUCCESS: تم إعادة إنشاء نافذة اللوائح والأقسام بالسنة الدراسية: {current_year}")

            except Exception as e:
                print(f"ERROR: فشل إعادة إنشاء نافذة اللوائح والأقسام: {e}")
                # في حالة الفشل، محاولة تحديث النافذة الموجودة بقوة
                if hasattr(self, 'windows') and 'lists_sections' in self.windows:
                    window = self.windows['lists_sections']
                    if hasattr(window, 'current_academic_year'):
                        window.current_academic_year = current_year
                    if hasattr(window, 'force_refresh_all_data'):
                        window.force_refresh_all_data()
                    elif hasattr(window, 'load_initial_data'):
                        window.load_initial_data()

        except Exception as e:
            print(f"ERROR: خطأ في إعادة تحميل نافذة اللوائح والأقسام: {e}")
            import traceback
            traceback.print_exc()

    def _reload_integrated_window(self, window_key, index):
        """إعادة تحميل النافذة المدمجة عند الضغط على التبويب"""
        try:
            # الحصول على السنة الدراسية الحالية
            current_year = self.get_current_academic_year()
            self.current_academic_year = current_year

            # قاموس النوافذ التي تحتاج إعادة تحميل خاصة
            special_reload_windows = {
                "lists_sections": self._reload_lists_sections_window,
                "main_window": self._reload_main_window,
                "institution_data": self._reload_institution_data_window,
                "general_statistics": self._reload_general_statistics_window,
                "absence_management": self._reload_absence_management_window,
                "exam_management": self._reload_exam_management_window
            }

            # إعادة تحميل النافذة المحددة
            if window_key in special_reload_windows:
                special_reload_windows[window_key]()
            else:
                # إعادة تحميل عامة للنوافذ الأخرى
                self._reload_generic_window(window_key)

            # عرض النافذة المحدثة
            window_widget = self.windows.get(window_key)
            if window_widget:
                self.show_window(window_widget, window_key)
                self.previous_tab_index = index

        except Exception as e:
            print(f"ERROR: خطأ في إعادة تحميل النافذة {window_key}: {e}")
            # في حالة الفشل، عرض النافذة كما هي
            window_widget = self.windows.get(window_key)
            if window_widget:
                self.show_window(window_widget, window_key)
                self.previous_tab_index = index

    def _reload_main_window(self):
        """إعادة تحميل النافذة الرئيسية"""
        try:
            if hasattr(self, 'windows') and 'main_window' in self.windows:
                window = self.windows['main_window']
                if hasattr(window, 'load_data'):
                    window.load_data()
                elif hasattr(window, 'refresh_data'):
                    window.refresh_data()
        except Exception as e:
            print(f"ERROR: خطأ في إعادة تحميل النافذة الرئيسية: {e}")

    def _reload_institution_data_window(self):
        """إعادة تحميل نافذة بيانات المؤسسة"""
        try:
            if hasattr(self, 'windows') and 'institution_data' in self.windows:
                window = self.windows['institution_data']
                if hasattr(window, 'institution_engine'):
                    # إعادة تحميل البيانات في محرك المؤسسة
                    if hasattr(window.institution_engine, 'load_institution_data'):
                        window.institution_engine.load_institution_data()
        except Exception as e:
            print(f"ERROR: خطأ في إعادة تحميل نافذة بيانات المؤسسة: {e}")

    def _reload_general_statistics_window(self):
        """إعادة تحميل نافذة الإحصائيات العامة"""
        try:
            if hasattr(self, 'windows') and 'general_statistics' in self.windows:
                window = self.windows['general_statistics']
                if hasattr(window, 'refresh_statistics'):
                    window.refresh_statistics()
                elif hasattr(window, 'load_data'):
                    window.load_data()
        except Exception as e:
            print(f"ERROR: خطأ في إعادة تحميل نافذة الإحصائيات: {e}")

    def _reload_absence_management_window(self):
        """إعادة تحميل نافذة مسك الغياب"""
        try:
            if hasattr(self, 'windows') and 'absence_management' in self.windows:
                window = self.windows['absence_management']
                if hasattr(window, 'update_academic_year'):
                    window.update_academic_year(self.current_academic_year)
                elif hasattr(window, 'refresh_data'):
                    window.refresh_data()
        except Exception as e:
            print(f"ERROR: خطأ في إعادة تحميل نافذة مسك الغياب: {e}")

    def _reload_exam_management_window(self):
        """إعادة تحميل نافذة مسك الفروض"""
        try:
            if hasattr(self, 'windows') and 'exam_management' in self.windows:
                window = self.windows['exam_management']
                if hasattr(window, 'update_academic_year'):
                    window.update_academic_year(self.current_academic_year)
                elif hasattr(window, 'refresh_data'):
                    window.refresh_data()
        except Exception as e:
            print(f"ERROR: خطأ في إعادة تحميل نافذة مسك الفروض: {e}")

    def _reload_generic_window(self, window_key):
        """إعادة تحميل عامة للنوافذ الأخرى"""
        try:
            if hasattr(self, 'windows') and window_key in self.windows:
                window = self.windows[window_key]

                # محاولة تحديث السنة الدراسية
                if hasattr(window, 'update_academic_year'):
                    window.update_academic_year(self.current_academic_year)
                elif hasattr(window, 'current_academic_year'):
                    window.current_academic_year = self.current_academic_year

                # محاولة إعادة تحميل البيانات
                if hasattr(window, 'refresh_data'):
                    window.refresh_data()
                elif hasattr(window, 'load_data'):
                    window.load_data()
                elif hasattr(window, 'load_initial_data'):
                    window.load_initial_data()
                elif hasattr(window, 'update_data'):
                    window.update_data()

        except Exception as e:
            print(f"ERROR: خطأ في الإعادة التحميل العامة للنافذة {window_key}: {e}")

    def _create_top_navbar(self):
        """إنشاء شريط التبويب العلوي المبسطة"""
        self.navbar_frame = QFrame()
        self.navbar_frame.setObjectName("NavBarFrame")
        
        navbar_style = """
            QFrame#NavBarFrame {
                background-color: #00382E;
                border-bottom: 1px solid #00382E;
                height: 40px;
                min-height: 40px;
                max-height: 40px;
                margin: 0;
                padding: 10;
            }
        """
        self.navbar_frame.setStyleSheet(navbar_style)

        navbar_layout = QVBoxLayout(self.navbar_frame)
        navbar_layout.setContentsMargins(0, 0, 0, 0)
        navbar_layout.setSpacing(0)

        self.tabWidget = TwoRowTabComponent()
        self.tabWidget.setObjectName("MainTabBar")

        clean_tab_style = """
            QTabBar {
                background-color: transparent;
            }
            QTabBar::tab {
                background-color: #B22222;
                color: #FFFFFF;
                font-family: 'Calibri';
                font-size: 11pt;
                font-weight: bold;
                min-width: 100px;
                height: 30px;
                padding: 1px 5px;
                margin: 1px 1px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                text-align: center;
            }
            QTabBar::tab:selected {
                background-color: #8B0000;
                color: white;
                border-bottom: 3px solid #CD5C5C;
            }
            QTabBar::tab:hover:!selected {
                background-color: #DC143C;
                color: white;
            }
            QTabBar::tab:disabled {
                color: #A9A9A9;
                background-color: #D3D3D3;
            }
        """
        
        self.tabWidget.setStyleSheet(clean_tab_style)        # قائمة التبويبات المحدثة - تبويب واحد فقط مع قائمة منسدلة
        self.navbar_items = [
            ("النافذة الرئيسية", "main_window"),
            ("إعدادات البرنامج", "program_settings"),  # التبويب الرئيسي الجديد
            ("اللوائح والأقسام", "lists_sections"),
            ("سجلات التلاميذ", "student_records"),  # تبويب سجلات التلاميذ الجديد
            ("مسك الغياب", "absence_management"),  # تبويب مسك الغياب الجديد
            ("مسك الفروض", "exam_management"),  # تبويب مسك الفروض الجديد
            ("أوراق التنقيط", "grading_sheets"),  # تبويب أوراق التنقيط
            ("اخبار بمهمة", "news_management"),  # تبويب إدارة الأخبار والنشاطات
            ("تسجيل الخروج", "logout_action"),  # تبويب تسجيل الخروج في النهاية
        ]

        # التبويبات التي ستكون في القائمة المنسدلة لإعدادات البرنامج
        self.dropdown_items = [
            ("1 استيراد البيانات وتحيينها", "teachers_subjects", "#1e3a8a"),  # أزرق غامق
            ("2 بيانات المؤسسة", "institution_data", "#1e3a8a"),  # أزرق غامق
            ("3 إسناد الحراسة", "educational_structure", "#1e3a8a"),  # أزرق غامق
            ("4 تعديل المسميات", "financial_management", "#1e3a8a"),  # أزرق غامق
            ("5 إعدادات الطابعة", "printer_settings", "#1e3a8a"),  # أزرق غامق
            ("6 الإحصائيات", "general_statistics", "#1e3a8a"),  # أزرق غامق
            ("7 تهيئة البرنامج", "program_init", "#1e3a8a")  # أزرق غامق
        ]

        # التبويبات التي ستكون في القائمة المنسدلة لسجلات التلاميذ
        self.student_records_dropdown_items = [
            (" سجلات المخالفات", "violations_records", "#87CEEB"),  # أزرق فاتح
            (" سجلات الدخول والتأخر", "entry_late_records", "#87CEEB"),  # أزرق فاتح
            (" سجلات مسك تبرير الغياب", "absence_justification_records", "#87CEEB"),  # أزرق فاتح
            (" سجلات زيارة أولياء الأمور", "parent_visit_records", "#87CEEB"),  # أزرق فاتح
            (" نافذة بحث", "search_window_html", "#87CEEB"),  # أزرق فاتح
            (" طلبات الشهادات المدرسية", "school_certificates_management", "#87CEEB"),  # أزرق فاتح
            (" طباعة اللوائح", "print_lists_student_records", "#87CEEB")  # أزرق فاتح
        ]

        self.tab_pages = {}
        self.navbar_buttons = {}

        for index, (text, window_key) in enumerate(self.navbar_items):
            tab_page = QWidget()
            tab_page.setObjectName(f"TabPage_{window_key}")
            self.tab_pages[window_key] = tab_page

            tab_index = self.tabWidget.addTab(tab_page, text)
            self.tabWidget.setTabData(tab_index, window_key)
            self.navbar_buttons[window_key] = {"tab_index": tab_index}

        navbar_layout.addWidget(self.tabWidget)
        self.tabWidget.connectCurrentChanged(self._on_tab_data_changed)

        # إنشاء القائمة المنسدلة لبيانات المؤسسة
        self._create_institution_dropdown()

        # إنشاء القائمة المنسدلة لسجلات التلاميذ
        self._create_student_records_dropdown()

        # اختبار بسيط للقائمة المنسدلة
        QTimer.singleShot(1000, self._test_dropdown_creation)

    def _create_student_records_dropdown(self):
        """إنشاء القائمة المنسدلة لتبويب سجلات التلاميذ"""
        try:
            # العثور على فهرس تبويب سجلات التلاميذ
            self.student_records_tab_index = None
            for window_key, tab_data in self.navbar_buttons.items():
                if window_key == "student_records":
                    self.student_records_tab_index = tab_data["tab_index"]
                    break

            if self.student_records_tab_index is None:
                print("WARNING: لم يتم العثور على تبويب سجلات التلاميذ")
                return

            # إنشاء ويدجيت القائمة المنسدلة لسجلات التلاميذ
            self.student_records_dropdown_widget = QWidget(self.central_widget)
            self.student_records_dropdown_widget.setObjectName("StudentRecordsDropdown")
            self.student_records_dropdown_widget.setVisible(False)  # مخفي في البداية
            self.student_records_dropdown_widget.setFixedHeight(0)  # ارتفاع صفر في البداية
            self.student_records_dropdown_widget.setStyleSheet("""
                QWidget#StudentRecordsDropdown {
                    background-color: #f8f9fa;
                    border-left: 3px solid #e74c3c;
                    border-right: 3px solid #e74c3c;
                    border-bottom: 3px solid #e74c3c;
                    border-top: none;
                    border-radius: 0 0 12px 12px;
                    margin: 0;
                    padding: 0;
                }
            """)

            # إنشاء تخطيط أفقي للأزرار (صف واحد)
            layout = QHBoxLayout(self.student_records_dropdown_widget)
            layout.setContentsMargins(15, 12, 15, 12)
            layout.setSpacing(10)

            # استخدام بيانات الأزرار من student_records_dropdown_items
            buttons_data = self.student_records_dropdown_items

            # إنشاء الأزرار
            for text, window_key, color in buttons_data:
                button = QPushButton(text)
                button.setFixedHeight(35)
                button.setMinimumWidth(180)
                button.setCursor(Qt.PointingHandCursor)
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                   stop: 0 {color}15, stop: 1 {color}25);
                        border: 1px solid {color};
                        border-radius: 6px;
                        padding: 6px 12px;
                        font-family: 'Calibri';
                        font-size: 16px;
                        font-weight: bold;
                        color: #4682B4;
                        text-align: center;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                   stop: 0 {color}30, stop: 1 {color}50);
                        color: white;
                        border-color: {color};
                    }}
                    QPushButton:pressed {{
                        background: {color};
                        color: white;
                    }}
                """)

                # ربط الزر بالوظيفة المناسبة
                if window_key == "violations_records":
                    button.clicked.connect(self._open_violations_window)
                elif window_key == "print_lists_student_records":
                    button.clicked.connect(self._open_print_lists_window)
                else:
                    button.clicked.connect(lambda checked, wk=window_key: self._navigate_to_student_dropdown_tab(wk))
                
                layout.addWidget(button)

            # إضافة القائمة للتخطيط الرئيسي
            navbar_index = self.main_layout.indexOf(self.navbar_frame)
            print(f"INFO: فهرس navbar_frame: {navbar_index}")

            if navbar_index >= 0:
                # إضافة بعد القائمة المنسدلة الأولى إذا كانت موجودة
                if hasattr(self, 'dropdown_widget'):
                    dropdown_index = self.main_layout.indexOf(self.dropdown_widget)
                    self.main_layout.insertWidget(dropdown_index + 1, self.student_records_dropdown_widget)
                else:
                    self.main_layout.insertWidget(navbar_index + 1, self.student_records_dropdown_widget)
                print("INFO: تم إدراج القائمة المنسدلة لسجلات التلاميذ")
            else:
                # إضافة في بداية التخطيط إذا لم نجد navbar_frame
                self.main_layout.insertWidget(1, self.student_records_dropdown_widget)
                print("INFO: تم إدراج القائمة في بداية التخطيط")

            print("INFO: تم إنشاء القائمة المنسدلة لسجلات التلاميذ بنجاح")

        except Exception as e:
            print(f"ERROR: خطأ في إنشاء القائمة المنسدلة لسجلات التلاميذ: {e}")
            import traceback
            traceback.print_exc()

    def _open_school_certificates_window(self):
        """فتح نافذة طلبات الشهادات المدرسية في كامل الشاشة"""
        try:
            print("INFO: محاولة فتح نافذة طلبات الشهادات المدرسية...")

            # إغلاق النافذة السابقة إذا كانت موجودة
            if hasattr(self, 'school_certificates_window') and self.school_certificates_window is not None:
                try:
                    self.school_certificates_window.close()
                    print("INFO: تم إغلاق النافذة السابقة")
                except (RuntimeError, AttributeError):
                    # النافذة تم حذفها من الذاكرة بالفعل
                    print("INFO: النافذة السابقة تم حذفها من الذاكرة")
                    pass

            # تنظيف المرجع
            self.school_certificates_window = None

            # استيراد نافذة طلبات الشهادات المدرسية
            from sub19_window_html import SchoolCertificateHtmlWindow

            # إنشاء النافذة وحفظ مرجع لها
            self.school_certificates_window = SchoolCertificateHtmlWindow(
                parent=None,  # بدون parent لضمان فتحها في كامل الشاشة
                db=self.qsql_db if hasattr(self, 'qsql_db') else None
            )

            # ربط إشارة الإغلاق لتنظيف المرجع بطريقة آمنة
            def cleanup_window_reference():
                if hasattr(self, 'school_certificates_window'):
                    self.school_certificates_window = None
                    print("INFO: تم تنظيف مرجع نافذة الشهادات المدرسية")

            # التحقق من أن النافذة تم إنشاؤها بنجاح قبل ربط الإشارة
            if self.school_certificates_window is not None:
                self.school_certificates_window.destroyed.connect(cleanup_window_reference)

            # فتح النافذة في كامل الشاشة
            self.school_certificates_window.show()
            self.school_certificates_window.showMaximized()

            print("✅ تم فتح نافذة طلبات الشهادات المدرسية بنجاح")

        except ImportError as e:
            print(f"ERROR: خطأ في استيراد نافذة طلبات الشهادات المدرسية: {e}")
            QMessageBox.critical(
                self,
                "خطأ في الاستيراد",
                f"تعذر استيراد نافذة طلبات الشهادات المدرسية:\n{str(e)}\n\nتأكد من وجود ملف sub19_window_html.py"
            )
        except Exception as e:
            print(f"ERROR: خطأ عام في فتح نافذة طلبات الشهادات المدرسية: {e}")
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ في فتح نافذة طلبات الشهادات المدرسية:\n{str(e)}"
            )

    def _open_violations_window(self):
        """فتح نافذة سجلات المخالفات في كامل الشاشة"""
        try:
            print("INFO: محاولة فتح نافذة سجلات المخالفات...")

            # إخفاء القائمة المنسدلة أولاً
            self._hide_student_records_dropdown()

            # إغلاق النافذة السابقة إذا كانت موجودة
            if hasattr(self, 'violations_window') and self.violations_window is not None:
                try:
                    self.violations_window.close()
                    print("INFO: تم إغلاق نافذة المخالفات السابقة")
                except:
                    pass
                finally:
                    self.violations_window = None

            # استيراد نافذة سجلات المخالفات
            from sub13_window_html import ViolationsViewerWindow

            # إنشاء النافذة وحفظ مرجع لها
            self.violations_window = ViolationsViewerWindow(parent=None)

            # ربط إشارة الإغلاق لتنظيف المرجع (فقط إذا كانت النافذة موجودة)
            if self.violations_window is not None:
                self.violations_window.destroyed.connect(
                    lambda: setattr(self, 'violations_window', None)
                )

                # فتح النافذة في كامل الشاشة باستخدام الدالة المحسنة
                self.violations_window.ensure_maximized()

                print("INFO: تم فتح نافذة سجلات المخالفات بنجاح وحفظ مرجع لها")

        except ImportError as e:
            print(f"ERROR: فشل استيراد sub13_window_html: {e}")
            QMessageBox.critical(
                self,
                "خطأ",
                "تعذر تحميل نافذة سجلات المخالفات\n\nتأكد من وجود ملف sub13_window_html.py"
            )
        except Exception as e:
            print(f"ERROR: خطأ في فتح نافذة سجلات المخالفات: {e}")
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ في فتح نافذة سجلات المخالفات:\n{str(e)}"
            )

    def _open_absence_justification_records_window(self):
        """فتح نافذة سجلات مسك تبرير الغياب في كامل الشاشة"""
        try:
            print("INFO: محاولة فتح نافذة سجلات مسك تبرير الغياب...")

            # إغلاق النافذة السابقة إذا كانت موجودة
            if hasattr(self, 'absence_justification_records_window') and self.absence_justification_records_window is not None:
                try:
                    self.absence_justification_records_window.close()
                    print("INFO: تم إغلاق النافذة السابقة")
                except:
                    pass
                finally:
                    self.absence_justification_records_window = None

            # استيراد نافذة سجلات مسك تبرير الغياب
            from sub17_window_html import AbsenceJustificationViewerWindow

            # إنشاء النافذة وحفظ مرجع لها
            self.absence_justification_records_window = AbsenceJustificationViewerWindow(
                parent=None  # بدون parent لضمان فتحها في كامل الشاشة
            )

            # ربط إشارة الإغلاق لتنظيف المرجع (فقط إذا كانت النافذة موجودة)
            if self.absence_justification_records_window is not None:
                self.absence_justification_records_window.destroyed.connect(
                    lambda: setattr(self, 'absence_justification_records_window', None)
                )

                # فتح النافذة في كامل الشاشة
                self.absence_justification_records_window.show()
                self.absence_justification_records_window.showMaximized()

                print("✅ تم فتح نافذة سجلات مسك تبرير الغياب بنجاح")

        except ImportError as e:
            print(f"ERROR: خطأ في استيراد نافذة سجلات مسك تبرير الغياب: {e}")
            QMessageBox.critical(
                self,
                "خطأ في الاستيراد",
                f"تعذر استيراد نافذة سجلات مسك تبرير الغياب:\n{str(e)}\n\nتأكد من وجود ملف sub17_window_html.py"
            )
        except Exception as e:
            print(f"ERROR: خطأ عام في فتح نافذة سجلات مسك تبرير الغياب: {e}")
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ في فتح نافذة سجلات مسك تبرير الغياب:\n{str(e)}"
            )

    def _open_parent_visit_records_window(self):
        """فتح نافذة سجلات زيارة أولياء الأمور في كامل الشاشة"""
        try:
            print("INFO: محاولة فتح نافذة سجلات زيارة أولياء الأمور...")

            # إغلاق النافذة السابقة إذا كانت موجودة
            if hasattr(self, 'parent_visit_records_window') and self.parent_visit_records_window is not None:
                try:
                    self.parent_visit_records_window.close()
                    print("INFO: تم إغلاق النافذة السابقة")
                except:
                    pass
                finally:
                    self.parent_visit_records_window = None

            # استيراد نافذة سجلات زيارة أولياء الأمور
            from sub16_window_html import ParentVisitViewerWindow

            # إنشاء النافذة وحفظ مرجع لها
            self.parent_visit_records_window = ParentVisitViewerWindow(
                parent=None  # بدون parent لضمان فتحها في كامل الشاشة
            )

            # ربط إشارة الإغلاق لتنظيف المرجع (فقط إذا كانت النافذة موجودة)
            if self.parent_visit_records_window is not None:
                self.parent_visit_records_window.destroyed.connect(
                    lambda: setattr(self, 'parent_visit_records_window', None)
                )

                # فتح النافذة في كامل الشاشة
                self.parent_visit_records_window.show()
                self.parent_visit_records_window.showMaximized()

                print("✅ تم فتح نافذة سجلات زيارة أولياء الأمور بنجاح")

        except ImportError as e:
            print(f"ERROR: خطأ في استيراد نافذة سجلات زيارة أولياء الأمور: {e}")
            QMessageBox.critical(
                self,
                "خطأ في الاستيراد",
                f"تعذر استيراد نافذة سجلات زيارة أولياء الأمور:\n{str(e)}\n\nتأكد من وجود ملف sub16_window_html.py"
            )
        except Exception as e:
            print(f"ERROR: خطأ عام في فتح نافذة سجلات زيارة أولياء الأمور: {e}")
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ في فتح نافذة سجلات زيارة أولياء الأمور:\n{str(e)}"
            )

    def _navigate_to_student_dropdown_tab(self, window_key):
        """التنقل إلى النافذة من القائمة المنسدلة لسجلات التلاميذ"""
        try:
            # إخفاء القائمة المنسدلة
            self._hide_student_records_dropdown()

            print(f"INFO: تم النقر على: {window_key}")

            # التعامل مع النوافذ المختلفة
            if window_key == "entry_late_records":
                self._open_entry_late_records_window()
            elif window_key == "violations_records":
                self._open_violations_window()
            elif window_key == "absence_justification_records":
                self._open_absence_justification_records_window()
            elif window_key == "parent_visit_records":
                self._open_parent_visit_records_window()
            elif window_key == "search_window_html":
                self._open_search_window_html()
            elif window_key == "school_certificates_management":
                self._open_school_certificates_management_window()
            elif window_key == "print_lists_student_records":
                self._open_print_lists_window()
            else:
                # النوافذ الأخرى قيد التطوير
                QMessageBox.information(
                    self,
                    "قيد التطوير",
                    f"سيتم إضافة نافذة {window_key} قريباً"
                )

        except Exception as e:
            print(f"ERROR: خطأ في التنقل: {e}")

    def _open_print_lists_window(self):
        """فتح نافذة طباعة اللوائح في كامل الشاشة"""
        try:
            print("INFO: محاولة فتح نافذة طباعة اللوائح...")

            # إغلاق النافذة السابقة إذا كانت موجودة
            if hasattr(self, 'print_lists_window') and self.print_lists_window:
                try:
                    self.print_lists_window.close()
                    print("INFO: تم إغلاق نافذة طباعة اللوائح السابقة")
                except:
                    pass

            # استيراد نافذة طباعة اللوائح
            from sub20_window import PrintListsWindow

            # إنشاء النافذة وحفظ مرجع لها
            self.print_lists_window = PrintListsWindow(
                parent=None,  # بدون parent لضمان فتحها في كامل الشاشة
                db=self.qsql_db if hasattr(self, 'qsql_db') else None,
                academic_year=self.current_academic_year
            )

            # ربط إشارة الإغلاق لتنظيف المرجع بطريقة آمنة
            def cleanup_window_reference():
                if hasattr(self, 'print_lists_window'):
                    self.print_lists_window = None
                    print("INFO: تم تنظيف مرجع نافذة طباعة اللوائح")

            # التحقق من أن النافذة تم إنشاؤها بنجاح قبل ربط الإشارة
            if self.print_lists_window is not None:
                self.print_lists_window.destroyed.connect(cleanup_window_reference)

            # فتح النافذة في كامل الشاشة
            self.print_lists_window.show()
            self.print_lists_window.showMaximized()

            print("✅ تم فتح نافذة طباعة اللوائح بنجاح")

        except ImportError as e:
            print(f"ERROR: خطأ في استيراد نافذة طباعة اللوائح: {e}")
            QMessageBox.critical(
                self,
                "خطأ في الاستيراد",
                f"تعذر استيراد نافذة طباعة اللوائح:\n{str(e)}\n\nتأكد من وجود ملف sub20_window.py"
            )
        except Exception as e:
            print(f"ERROR: خطأ عام في فتح نافذة طباعة اللوائح: {e}")
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ في فتح نافذة طباعة اللوائح:\n{str(e)}"
            )

    def _open_entry_late_records_window(self):
        """فتح نافذة سجلات الدخول والتأخر في كامل الشاشة"""
        try:
            print("INFO: محاولة فتح نافذة سجلات الدخول والتأخر...")

            # إغلاق النافذة السابقة إذا كانت موجودة
            if hasattr(self, 'entry_records_window') and self.entry_records_window is not None:
                try:
                    self.entry_records_window.close()
                    print("INFO: تم إغلاق النافذة السابقة")
                except:
                    pass
                finally:
                    self.entry_records_window = None

            # استيراد نافذة سجلات الدخول والتأخر
            from sub11_window_html import UniversalStudentRecordsWindow

            # إنشاء النافذة مع نوع السجل المطلوب وحفظ مرجع لها
            self.entry_records_window = UniversalStudentRecordsWindow(
                record_type="entry_permissions",
                parent=None  # بدون parent لضمان فتحها في كامل الشاشة
            )

            # ربط إشارة الإغلاق لتنظيف المرجع (فقط إذا كانت النافذة موجودة)
            if self.entry_records_window is not None:
                self.entry_records_window.destroyed.connect(
                    lambda: setattr(self, 'entry_records_window', None)
                )

                # فتح النافذة في كامل الشاشة باستخدام الدالة المحسنة
                self.entry_records_window.ensure_maximized()

                print("INFO: تم فتح نافذة سجلات الدخول والتأخر بنجاح وحفظ مرجع لها")

        except ImportError as ie:
            print(f"ERROR: خطأ في استيراد نافذة سجلات الدخول والتأخر: {ie}")
            QMessageBox.critical(
                self,
                "خطأ في الاستيراد",
                f"لم يتم العثور على ملف نافذة سجلات الدخول والتأخر:\n{str(ie)}"
            )
        except Exception as e:
            print(f"ERROR: خطأ في فتح نافذة سجلات الدخول والتأخر: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ في فتح نافذة سجلات الدخول والتأخر:\n{str(e)}"
            )

    def _show_student_records_dropdown(self):
        """إظهار القائمة المنسدلة لسجلات التلاميذ"""
        try:
            if hasattr(self, 'student_records_dropdown_widget'):
                print("INFO: إظهار القائمة المنسدلة لسجلات التلاميذ...")
                self.student_records_dropdown_widget.setVisible(True)
                self.student_records_dropdown_widget.setFixedHeight(59)  # ارتفاع الأزرار + الحشو
                self.student_records_dropdown_widget.show()
                print(f"INFO: تم إظهار القائمة - الارتفاع: {self.student_records_dropdown_widget.height()}")
        except Exception as e:
            print(f"ERROR: خطأ في إظهار القائمة المنسدلة لسجلات التلاميذ: {e}")

    def _hide_student_records_dropdown(self):
        """إخفاء القائمة المنسدلة لسجلات التلاميذ"""
        try:
            if hasattr(self, 'student_records_dropdown_widget'):
                self.student_records_dropdown_widget.setFixedHeight(0)
                self.student_records_dropdown_widget.setVisible(False)
                print("INFO: تم إخفاء القائمة المنسدلة لسجلات التلاميذ")
        except Exception as e:
            print(f"ERROR: خطأ في إخفاء القائمة المنسدلة لسجلات التلاميذ: {e}")

    def _open_search_window_html(self):
        """فتح نافذة البحث HTML في كامل الشاشة"""
        try:
            print("INFO: محاولة فتح نافذة البحث HTML...")

            # إغلاق النافذة السابقة إذا كانت موجودة
            if hasattr(self, 'search_html_window') and self.search_html_window:
                try:
                    self.search_html_window.close()
                    print("INFO: تم إغلاق نافذة البحث HTML السابقة")
                except:
                    pass

            # استيراد نافذة البحث HTML
            from sub9_window__html import GeneralRecordHtmlWindow

            # إنشاء النافذة بدون parent لضمان فتحها في كامل الشاشة
            self.search_html_window = GeneralRecordHtmlWindow(parent=None)

            # تعيين خاصية لتنظيف المرجع عند الإغلاق
            def cleanup_search_window():
                self.search_html_window = None

            # ربط دالة التنظيف بحدث الإغلاق
            original_close_event = self.search_html_window.closeEvent
            def close_event_wrapper(event):
                cleanup_search_window()
                if original_close_event:
                    original_close_event(event)
                else:
                    event.accept()
            self.search_html_window.closeEvent = close_event_wrapper

            # عرض النافذة في كامل الشاشة
            self.search_html_window.showMaximized()
            print("INFO: تم فتح نافذة البحث HTML في كامل الشاشة")

        except ImportError as e:
            print(f"ERROR: فشل استيراد نافذة البحث HTML: {e}")
            QMessageBox.critical(
                self,
                "خطأ",
                "تعذر فتح نافذة البحث HTML. يرجى التأكد من وجود ملف sub9_window__html.py"
            )
        except Exception as e:
            print(f"ERROR: خطأ عام في فتح نافذة البحث HTML: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ في فتح نافذة البحث HTML:\n{str(e)}"
            )

    def _open_school_certificates_management_window(self):
        """فتح نافذة إدارة طلبات الشهادات المدرسية في كامل الشاشة"""
        try:
            print("INFO: محاولة فتح نافذة إدارة طلبات الشهادات المدرسية...")

            # إغلاق النافذة السابقة إذا كانت موجودة
            if hasattr(self, 'certificates_management_window') and self.certificates_management_window:
                try:
                    self.certificates_management_window.close()
                    print("INFO: تم إغلاق نافذة إدارة الشهادات السابقة")
                except:
                    pass

            # استيراد نافذة إدارة طلبات الشهادات المدرسية
            from sub19_window_html import SchoolCertificateHtmlWindow

            # إنشاء النافذة بدون parent لضمان فتحها في كامل الشاشة
            self.certificates_management_window = SchoolCertificateHtmlWindow(parent=None)

            # تعيين خاصية لتنظيف المرجع عند الإغلاق
            def cleanup_certificates_window():
                self.certificates_management_window = None

            # ربط دالة التنظيف بحدث الإغلاق
            original_close_event = self.certificates_management_window.closeEvent
            def close_event_wrapper(event):
                cleanup_certificates_window()
                if original_close_event:
                    original_close_event(event)
                else:
                    event.accept()
            self.certificates_management_window.closeEvent = close_event_wrapper

            # عرض النافذة في كامل الشاشة
            self.certificates_management_window.showMaximized()
            print("INFO: تم فتح نافذة إدارة طلبات الشهادات المدرسية في كامل الشاشة")

        except ImportError as e:
            print(f"ERROR: فشل استيراد نافذة إدارة الشهادات: {e}")
            QMessageBox.critical(
                self,
                "خطأ",
                "تعذر فتح نافذة إدارة طلبات الشهادات المدرسية. يرجى التأكد من وجود ملف sub19_window_html.py"
            )
        except Exception as e:
            print(f"ERROR: خطأ عام في فتح نافذة إدارة الشهادات: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ في فتح نافذة إدارة طلبات الشهادات المدرسية:\n{str(e)}"
            )

    def _create_institution_dropdown(self):
        """إنشاء القائمة المنسدلة لتبويب بيانات المؤسسة"""
        try:
            # العثور على فهرس تبويب إعدادات البرنامج
            self.program_settings_tab_index = None
            for window_key, tab_data in self.navbar_buttons.items():
                if window_key == "program_settings":
                    self.program_settings_tab_index = tab_data["tab_index"]
                    break

            if self.program_settings_tab_index is None:
                print("WARNING: لم يتم العثور على تبويب إعدادات البرنامج")
                return

            # إنشاء ويدجيت القائمة المنسدلة كجزء مدمج
            self.dropdown_widget = QWidget(self.central_widget)
            self.dropdown_widget.setObjectName("InstitutionDropdown")
            self.dropdown_widget.setVisible(False)  # مخفي في البداية
            self.dropdown_widget.setFixedHeight(0)  # ارتفاع صفر في البداية
            self.dropdown_widget.setStyleSheet("""
                QWidget#InstitutionDropdown {
                    background-color: #f8f9fa;
                    border-left: 3px solid #87CEEB;
                    border-right: 3px solid #87CEEB;
                    border-bottom: 3px solid #87CEEB;
                    border-top: none;
                    border-radius: 0 0 12px 12px;
                    margin: 0;
                    padding: 0;
                }
            """)

            # إنشاء تخطيط أفقي للأزرار (صف واحد)
            layout = QHBoxLayout(self.dropdown_widget)
            layout.setContentsMargins(15, 12, 15, 12)
            layout.setSpacing(10)

            # استخدام بيانات الأزرار من dropdown_items
            buttons_data = self.dropdown_items

            # إنشاء الأزرار
            for text, window_key, color in buttons_data:
                button = QPushButton(text)
                button.setFixedHeight(35)
                button.setMinimumWidth(180)
                button.setCursor(Qt.PointingHandCursor)
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                   stop: 0 {color}15, stop: 1 {color}25);
                        border: 1px solid {color};
                        border-radius: 6px;
                        padding: 6px 12px;
                        font-family: 'Calibri';
                        font-size: 14px;
                        font-weight: bold;
                        color: #1e3a8a;
                        text-align: center;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                   stop: 0 {color}30, stop: 1 {color}50);
                        color: white;
                        border-color: #1e3a8a;
                    }}
                    QPushButton:pressed {{
                        background: #1e3a8a;
                        color: white;
                    }}
                """)

                # ربط الزر بالوظيفة
                button.clicked.connect(lambda checked, wk=window_key: self._navigate_to_dropdown_tab(wk))
                layout.addWidget(button)

            # إضافة القائمة للتخطيط الرئيسي
            navbar_index = self.main_layout.indexOf(self.navbar_frame)
            print(f"INFO: فهرس navbar_frame: {navbar_index}")

            if navbar_index >= 0:
                # إضافة بعد navbar_frame
                self.main_layout.insertWidget(navbar_index + 1, self.dropdown_widget)
                print("INFO: تم إدراج القائمة بعد navbar_frame")
            else:
                # إضافة في بداية التخطيط إذا لم نجد navbar_frame
                self.main_layout.insertWidget(1, self.dropdown_widget)
                print("INFO: تم إدراج القائمة في بداية التخطيط")

            print(f"INFO: عدد العناصر في التخطيط: {self.main_layout.count()}")
            print(f"INFO: حجم القائمة: {self.dropdown_widget.size()}")
            print(f"INFO: مرئية: {self.dropdown_widget.isVisible()}")
            print(f"INFO: ارتفاع: {self.dropdown_widget.height()}")
            print("INFO: تم إنشاء القائمة المنسدلة بنجاح")

        except Exception as e:
            print(f"ERROR: خطأ في إنشاء القائمة المنسدلة: {e}")
            import traceback
            traceback.print_exc()

    def _test_dropdown_creation(self):
        """اختبار إنشاء القائمة المنسدلة"""
        try:
            print("🧪 اختبار إنشاء القائمة المنسدلة...")
            if hasattr(self, 'dropdown_widget'):
                print("✅ dropdown_widget موجود")
                print(f"   الحجم: {self.dropdown_widget.size()}")
                print(f"   الارتفاع: {self.dropdown_widget.height()}")
                print(f"   مرئي: {self.dropdown_widget.isVisible()}")

                # التحقق من الوالد
                parent = self.dropdown_widget.parent()
                if parent:
                    print(f"   الوالد: {parent.objectName()}")
                else:
                    print("   ❌ لا يوجد والد")

                # محاولة إظهار القائمة للاختبار
                print("🔍 محاولة إظهار القائمة للاختبار...")
                self._show_dropdown()

                # التحقق بعد الإظهار
                print(f"   بعد الإظهار - الارتفاع: {self.dropdown_widget.height()}")
                print(f"   بعد الإظهار - مرئي: {self.dropdown_widget.isVisible()}")

                # إخفاء القائمة مرة أخرى
                QTimer.singleShot(2000, self._hide_dropdown)

            else:
                print("❌ dropdown_widget غير موجود")

        except Exception as e:
            print(f"ERROR: خطأ في اختبار القائمة المنسدلة: {e}")

    def _show_dropdown(self):
        """إظهار القائمة المنسدلة مع تأثير انزلاق"""
        try:
            if hasattr(self, 'dropdown_widget'):
                print("INFO: محاولة إظهار القائمة المنسدلة...")
                self.dropdown_widget.setVisible(True)
                self.dropdown_widget.setFixedHeight(59)  # ارتفاع الأزرار + الحشو
                self.dropdown_widget.show()  # تأكيد الإظهار
                print(f"INFO: تم إظهار القائمة المنسدلة - الارتفاع: {self.dropdown_widget.height()}")
            else:
                print("ERROR: dropdown_widget غير موجود")
        except Exception as e:
            print(f"ERROR: خطأ في إظهار القائمة المنسدلة: {e}")
            import traceback
            traceback.print_exc()

    def _hide_dropdown(self):
        """إخفاء القائمة المنسدلة مع تأثير انزلاق"""
        try:
            if hasattr(self, 'dropdown_widget'):
                self.dropdown_widget.setFixedHeight(0)
                self.dropdown_widget.setVisible(False)
                print("INFO: تم إخفاء القائمة المنسدلة")
        except Exception as e:
            print(f"ERROR: خطأ في إخفاء القائمة المنسدلة: {e}")

    def _navigate_to_dropdown_tab(self, window_key):
        """التنقل إلى النافذة من القائمة المنسدلة"""
        try:
            # إخفاء القائمة المنسدلة
            self._hide_dropdown()

            # التنقل للنافذة مباشرة
            if window_key in self.windows:
                window_widget = self.windows[window_key]
                self.show_window(window_widget)
                print(f"INFO: تم التنقل إلى النافذة: {window_key}")
            elif window_key in self.navbar_buttons:
                # التنقل للتبويب إذا لم توجد النافذة
                tab_index = self.navbar_buttons[window_key]["tab_index"]
                self.tabWidget.setCurrentIndex(tab_index)
                print(f"INFO: تم التنقل إلى التبويب: {window_key}")
            else:
                print(f"WARNING: لم يتم العثور على النافذة أو التبويب: {window_key}")

        except Exception as e:
            print(f"ERROR: خطأ في التنقل: {e}")
            import traceback
            traceback.print_exc()

    def _on_tab_data_changed(self, index, window_key):
        """معالجة حدث تغيير التبويب"""
        # التحقق من النقر على تبويب إعدادات البرنامج
        if window_key == "program_settings" and hasattr(self, 'dropdown_widget'):
            # إخفاء القائمة الأخرى أولاً
            if hasattr(self, 'student_records_dropdown_widget') and self.student_records_dropdown_widget.height() > 0:
                self._hide_student_records_dropdown()

            # التبديل بين إظهار وإخفاء القائمة المنسدلة
            if self.dropdown_widget.height() > 0:  # إذا كانت القائمة مفتوحة
                self._hide_dropdown()
            else:  # إذا كانت القائمة مغلقة
                self._show_dropdown()

            # السماح بالضغط على نفس التبويب مرة أخرى
            # عدم تغيير التبويب الحالي
            self.tabWidget.blockSignals(True)
            self.tabWidget.setCurrentIndex(self.previous_tab_index if hasattr(self, 'previous_tab_index') else 0)
            self.tabWidget.blockSignals(False)
            return

        # التحقق من النقر على تبويب سجلات التلاميذ
        if window_key == "student_records" and hasattr(self, 'student_records_dropdown_widget'):
            # إخفاء القائمة الأخرى أولاً
            if hasattr(self, 'dropdown_widget') and self.dropdown_widget.height() > 0:
                self._hide_dropdown()

            # التبديل بين إظهار وإخفاء القائمة المنسدلة لسجلات التلاميذ
            if self.student_records_dropdown_widget.height() > 0:  # إذا كانت القائمة مفتوحة
                self._hide_student_records_dropdown()
            else:  # إذا كانت القائمة مغلقة
                self._show_student_records_dropdown()

            # السماح بالضغط على نفس التبويب مرة أخرى
            # عدم تغيير التبويب الحالي
            self.tabWidget.blockSignals(True)
            self.tabWidget.setCurrentIndex(self.previous_tab_index if hasattr(self, 'previous_tab_index') else 0)
            self.tabWidget.blockSignals(False)
            return

        # إخفاء جميع القوائم المنسدلة عند النقر على تبويب آخر
        if hasattr(self, 'dropdown_widget') and self.dropdown_widget.isVisible():
            self._hide_dropdown()
        if hasattr(self, 'student_records_dropdown_widget') and self.student_records_dropdown_widget.isVisible():
            self._hide_student_records_dropdown()

        # إعادة تحميل النافذة المدمجة عند الضغط على أي تبويب
        self._reload_integrated_window(window_key, index)

        if window_key == "absence_management":
            # فتح نافذة مسك الغياب المدمجة
            print(f"INFO: التبديل إلى نافذة مسك الغياب المدمجة")
            window_widget = self.windows.get("absence_management")
            if window_widget:
                self.show_window(window_widget, window_key)
                self.previous_tab_index = index
            return

        if window_key == "exam_management":
            # فتح نافذة مسك الفروض المدمجة
            print(f"INFO: التبديل إلى نافذة مسك الفروض المدمجة")
            window_widget = self.windows.get("exam_management")
            if window_widget:
                self.show_window(window_widget, window_key)
                self.previous_tab_index = index
            return

        if window_key == "logout_action":
            self.tabWidget.blockSignals(True)
            self.tabWidget.setCurrentIndex(self.previous_tab_index)
            self.tabWidget.blockSignals(False)

            should_quit = self.show_logout_confirmation_dialog()
            if should_quit:
                QCoreApplication.instance().quit()

        elif window_key in self.windows:
            print(f"INFO: التبديل إلى النافذة: {window_key}")
            window_widget = self.windows[window_key]

            # التحقق من أن الكائن لم يتم حذفه
            try:
                # محاولة الوصول إلى خاصية بسيطة للتأكد من أن الكائن ما زال صالحاً
                _ = window_widget.objectName()
            except RuntimeError:
                print(f"ERROR: الكائن {window_key} تم حذفه من الذاكرة - إعادة إنشاء النافذة...")
                # إعادة إنشاء النافذة المحذوفة
                self._recreate_deleted_window(window_key)
                window_widget = self.windows.get(window_key)
                if not window_widget:
                    print(f"ERROR: فشل في إعادة إنشاء النافذة {window_key}")
                    return

            # تم إزالة المعالجة الخاصة لنافذة اللوائح والأقسام
            # الآن ستفتح كنافذة مدمجة مثل باقي النوافذ

            # عرض النافذة المطلوبة
            self.show_window(window_widget, window_key)
            self.previous_tab_index = index

        else:
            # في حالة عدم وجود النافذة، العودة للتبويب السابق
            self.tabWidget.blockSignals(True)
            self.tabWidget.setCurrentIndex(self.previous_tab_index)
            self.tabWidget.blockSignals(False)

    def _recreate_deleted_window(self, window_key):
        """إعادة إنشاء النافذة المحذوفة"""
        try:
            print(f"INFO: محاولة إعادة إنشاء النافذة {window_key}...")

            # لا توجد نوافذ تحتاج إعادة إنشاء حالياً
            print(f"WARNING: لا يمكن إعادة إنشاء النافذة {window_key} - غير مدعومة")

        except Exception as e:
            print(f"ERROR: خطأ عام في إعادة إنشاء النافذة {window_key}: {e}")

    def _link_navbar_buttons(self):
        """ربط أزرار شريط التنقل المبسطة"""
        for window_key, tab_data in self.navbar_buttons.items():
            tab_index = tab_data["tab_index"]

            if window_key == "logout_action":
                self.tabWidget.setTabEnabled(tab_index, True)
                self.tabWidget.setTabToolTip(tab_index, "تسجيل الخروج من البرنامج")
            elif window_key == "program_settings":
                # تفعيل تبويب إعدادات البرنامج لأن له قائمة منسدلة
                self.tabWidget.setTabEnabled(tab_index, True)
                self.tabWidget.setTabToolTip(tab_index, "إعدادات البرنامج - انقر لعرض القائمة المنسدلة")
            elif window_key == "student_records":
                # تفعيل تبويب سجلات التلاميذ لأن له قائمة منسدلة
                self.tabWidget.setTabEnabled(tab_index, True)
                self.tabWidget.setTabToolTip(tab_index, "سجلات التلاميذ - انقر لعرض القائمة المنسدلة")
            elif window_key == "school_certificates":
                # تفعيل تبويب طلبات الشهادات المدرسية
                self.tabWidget.setTabEnabled(tab_index, True)
                self.tabWidget.setTabToolTip(tab_index, "طلبات الشهادات المدرسية - انقر لفتح النافذة")
            elif window_key == "absence_management":
                # تفعيل تبويب مسك الغياب
                self.tabWidget.setTabEnabled(tab_index, True)
                self.tabWidget.setTabToolTip(tab_index, "مسك الغياب - انقر لفتح النافذة")
            elif window_key not in self.windows:
                self.tabWidget.setTabEnabled(tab_index, False)
                self.tabWidget.setTabToolTip(tab_index, "الوحدة غير متوفرة")

    def show_window(self, window_widget, window_key_debug=None):
        """عرض الويدجت المحدد في منطقة المحتوى"""
        try:
            if window_widget and isinstance(window_widget, QWidget):
                # التحقق من أن الكائن لم يتم حذفه
                try:
                    _ = window_widget.objectName()
                except RuntimeError:
                    return

                current_index_in_stack = self.content_area.indexOf(window_widget)
                if current_index_in_stack != -1:
                    self.content_area.setCurrentIndex(current_index_in_stack)
        except Exception:
            pass

    def _setup_content_area(self):
        """إعداد منطقة عرض المحتوى الرئيسية"""
        self.content_area = QStackedWidget()
        self.content_area.setObjectName("ContentArea")
        self.content_area.setStyleSheet("QWidget#ContentArea { background-color: #ECEFF1; margin-top: 0; }")
        self.content_area.setContentsMargins(0, 0, 0, 0)

    def _create_windows(self):
        """إنشاء النوافذ الفرعية المبسطة"""
        self.windows = {}

        # إنشاء النافذة الرئيسية من sub01_window
        try:
            from sub01_window import Sub01Window
            main_window = Sub01Window(parent=self)
            
            # تحويل النافذة إلى ويدجيت مدمج
            main_window.setWindowFlags(Qt.Widget)
            main_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            
            print("INFO: تم إنشاء النافذة الرئيسية من sub01_window بنجاح")
        except ImportError as e:
            print(f"WARNING: فشل استيراد sub01_window: {e}")
            # في حالة فشل الاستيراد، استخدام نافذة مؤقتة
            main_window = PlaceholderWindow(
                title="النافذة الرئيسية",
                message="تعذر تحميل النافذة الرئيسية\n\nتأكد من وجود ملف sub01_window.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء النافذة الرئيسية: {e}")
            # في حالة حدوث خطأ آخر، استخدام نافذة مؤقتة
            main_window = PlaceholderWindow(
                title="النافذة الرئيسية",
                message=f"حدث خطأ في تحميل النافذة الرئيسية:\n{str(e)}",
                parent=self
            )
  

        # إنشاء نافذة بيانات المؤسسة من sub2_window_html (الإصدار الحديث)
        try:
            print("INFO: محاولة تحميل نافذة بيانات المؤسسة الحديثة...")
            from sub2_window_html import InstitutionWindow
            institution_data_window = InstitutionWindow(parent=self)
            
            # تحويل النافذة إلى ويدجيت مدمج
            institution_data_window.setWindowFlags(Qt.Widget)
            institution_data_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            
            # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
            if hasattr(institution_data_window, 'menuBar'):
                institution_data_window.menuBar().setVisible(False)
            
            if hasattr(institution_data_window, 'statusBar'):
                institution_data_window.statusBar().setVisible(False)

            # ربط إشارة تغيير السنة الدراسية
            if hasattr(institution_data_window, 'institution_engine'):
                institution_data_window.institution_engine.academicYearChanged.connect(
                    self.on_academic_year_changed
                )
                print("INFO: تم ربط إشارة تغيير السنة الدراسية")

            print("INFO: تم إنشاء نافذة بيانات المؤسسة من sub2_window_html (الإصدار الحديث) بنجاح")
            
        except ImportError as e:
            print(f"WARNING: فشل استيراد sub2_window_html: {e}")
            # في حالة فشل استيراد النسخة الحديثة، استخدام نافذة مؤقتة
            institution_data_window = PlaceholderWindow(
                title="بيانات المؤسسة",
                message="تعذر تحميل نافذة بيانات المؤسسة\n\nتأكد من وجود ملف sub2_window_html.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة بيانات المؤسسة: {e}")
            # في حالة حدوث خطأ آخر، استخدام نافذة مؤقتة
            institution_data_window = PlaceholderWindow(
                title="بيانات المؤسسة",
                message=f"حدث خطأ في تحميل نافذة بيانات المؤسسة:\n{str(e)}",
                parent=self
            )

        # إنشاء نافذة الإحصائيات العامة من sub5_window_html
        try:
            print("INFO: محاولة تحميل نافذة الإحصائيات العامة...")
            from sub5_window_html import StatisticsWindow
            general_statistics_window = StatisticsWindow(parent=self, db_path=self.db_path)
            
            # تحويل النافذة إلى ويدجيت مدمج
            general_statistics_window.setWindowFlags(Qt.Widget)
            general_statistics_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            
            # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
            if hasattr(general_statistics_window, 'menuBar'):
                general_statistics_window.menuBar().setVisible(False)
            
            if hasattr(general_statistics_window, 'statusBar'):
                general_statistics_window.statusBar().setVisible(False)
            
            print("INFO: تم إنشاء نافذة الإحصائيات العامة من sub5_window_html بنجاح")
            
        except ImportError as e:
            print(f"WARNING: فشل استيراد sub5_window_html: {e}")
            general_statistics_window = PlaceholderWindow(
                title="الإحصائيات العامة",
                message="تعذر تحميل نافذة الإحصائيات العامة\n\nتأكد من وجود ملف sub5_window_html.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة الإحصائيات العامة: {e}")
            general_statistics_window = PlaceholderWindow(
                title="الإحصائيات العامة",
                message=f"حدث خطأ في تحميل نافذة الإحصائيات العامة:\n{str(e)}",
                parent=self
            )
        
        # إنشاء نافذة تهيئة البرنامج من sub8_window
        try:
            from sub8_window import Sub8Window
            program_init_window = Sub8Window(parent=self)
            program_init_window.setWindowFlags(Qt.Widget)
            program_init_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            print("INFO: تم إنشاء نافذة تهيئة البرنامج من sub8_window بنجاح")
        except ImportError as e:
            print(f"WARNING: فشل استيراد sub8_window: {e}")
            program_init_window = PlaceholderWindow(
                title="تهيئة البرنامج",
                message="تعذر تحميل نافذة تهيئة البرنامج\n\nتأكد من وجود ملف sub8_window.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة تهيئة البرنامج: {e}")
            program_init_window = PlaceholderWindow(
                title="تهيئة البرنامج",
                message=f"حدث خطأ في تحميل نافذة تهيئة البرنامج:\n{str(e)}",
                parent=self
            )

        # إنشاء نافذة اللوائح والأقسام من sub4_window
        try:
            print("INFO: بدء استيراد sub4_window...")
            
            # استيراد كلاس Sub4Window من sub4_window
            from sub4_window import Sub4Window
            
            print("INFO: تم استيراد Sub4Window بنجاح")
            
            # إنشاء مسار قاعدة البيانات
            db_path = self.db_path  # استخدام المسار المحدد مسبقاً
            print(f"INFO: مسار قاعدة البيانات المحدد: {db_path}")
            
            # إنشاء النافذة مع المعاملات المطلوبة
            print("INFO: إنشاء كائن Sub4Window...")
            lists_sections_window = Sub4Window(
                db=self.qsql_db,  # استخدام QSqlDatabase بدلاً من sqlite3.Connection
                academic_year=self.current_academic_year,
                parent=self
            )
            
            print("INFO: تم إنشاء كائن Sub4Window بنجاح")
            
            # التحقق من نوع النافذة قبل التحويل
            print(f"INFO: نوع النافذة قبل التحويل: {type(lists_sections_window).__name__}")
            print(f"INFO: الفئة الأب للنافذة: {[base.__name__ for base in type(lists_sections_window).__bases__]}")
            
            # تحويل النافذة إلى ويدجيت مدمج
            print("INFO: تحويل النافذة إلى ويدجيت مدمج...")
            lists_sections_window.setWindowFlags(Qt.Widget)
            lists_sections_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            print("INFO: تم تحديد خصائص النافذة بنجاح")
            
            # التحقق من خصائص النافذة
            if hasattr(lists_sections_window, 'table_lists'):
                print("INFO: تم العثور على جدول البيانات في النافذة")
            else:
                print("WARNING: لم يتم العثور على جدول البيانات في النافذة")
                
            if hasattr(lists_sections_window, 'load_initial_data'):
                print("INFO: تم العثور على دالة تحميل البيانات")
            else:
                print("WARNING: لم يتم العثور على دالة تحميل البيانات")
            
            # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
            if hasattr(lists_sections_window, 'menuBar'):
                print("INFO: إخفاء شريط القوائم...")
                lists_sections_window.menuBar().setVisible(False)
            else:
                print("INFO: النافذة لا تحتوي على شريط قوائم (مناسب للتكامل)")
                
            if hasattr(lists_sections_window, 'statusBar'):
                print("INFO: إخفاء شريط الحالة...")
                lists_sections_window.statusBar().setVisible(False)
            else:
                print("INFO: النافذة لا تحتوي على شريط حالة (مناسب للتكامل)")
            
            # التحقق من حجم النافذة
            window_size = lists_sections_window.size()
            print(f"INFO: حجم النافذة: {window_size.width()} x {window_size.height()}")
            
            # التحقق من اتجاه التخطيط
            layout_direction = lists_sections_window.layoutDirection()
            print(f"INFO: اتجاه التخطيط: {'من اليمين إلى اليسار' if layout_direction == Qt.RightToLeft else 'من اليسار إلى اليمين'}")
            
            print("INFO: تم إنشاء نافذة اللوائح والأقسام من sub4_window بنجاح")
            
        except ImportError as e:
            print(f"ERROR: فشل استيراد sub4_window: {e}")
            print("DIAGNOSTIC: الملفات المطلوبة:")
            print("  - sub4_window.py")
            print("  - كلاس Sub4Window")
            
            lists_sections_window = PlaceholderWindow(
                title="اللوائح والأقسام",
                message="تعذر تحميل نافذة اللوائح والأقسام\n\nتأكد من وجود ملف sub4_window.py وكلاس Sub4Window",
                parent=self
            )
            print("INFO: تم إنشاء نافذة مؤقتة للوائح والأقسام")
            
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة اللوائح والأقسام: {e}")
            print(f"ERROR TYPE: {type(e).__name__}")
            print(f"ERROR DETAILS: {str(e)}")
            
            # تشخيص إضافي للخطأ
            if "database" in str(e).lower():
                print("DIAGNOSTIC: الخطأ متعلق بقاعدة البيانات")
                print(f"DIAGNOSTIC: مسار قاعدة البيانات: {self.db_path}")
                print(f"DIAGNOSTIC: وجود ملف قاعدة البيانات: {os.path.exists(self.db_path)}")
            elif "init" in str(e).lower() or "__init__" in str(e).lower():
                print("DIAGNOSTIC: الخطأ في دالة التهيئة (__init__)")
                print("DIAGNOSTIC: تحقق من معاملات دالة التهيئة في الكلاس")
            elif "parent" in str(e).lower():
                print("DIAGNOSTIC: الخطأ متعلق بالنافذة الأب")
                print(f"DIAGNOSTIC: نوع النافذة الأب: {type(self).__name__}")
            
            lists_sections_window = PlaceholderWindow(
                title="اللوائح والأقسام",
                message=f"حدث خطأ في تحميل نافذة اللوائح والأقسام:\n{str(e)}",
                parent=self
            )
            print("INFO: تم إنشاء نافذة مؤقتة بديلة للوائح والأقسام")

        # إنشاء نافذة إعدادات الطابعة من sub7_window_html
        try:
            print("INFO: بدء استيراد sub7_window_html...")
            from sub7_window_html import PrinterSettingsWindow
            
            # إنشاء النافذة
            printer_settings_window = PrinterSettingsWindow(parent=self)
            
            # تحويل النافذة إلى ويدجيت مدمج
            printer_settings_window.setWindowFlags(Qt.Widget)
            printer_settings_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

            # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
            if hasattr(printer_settings_window, 'menuBar'):
                printer_settings_window.menuBar().setVisible(False)
            if hasattr(printer_settings_window, 'statusBar'):
                printer_settings_window.statusBar().setVisible(False)
            
            print("INFO: تم إنشاء نافذة إعدادات الطابعة من sub7_window_html بنجاح")
            
        except ImportError as e:
            print(f"ERROR: فشل استيراد sub7_window_html: {e}")
            printer_settings_window = PlaceholderWindow(
                title="إعدادات الطابعة",
                message="تعذر تحميل نافذة إعدادات الطابعة\n\nتأكد من وجود ملف sub7_window_html.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة إعدادات الطابعة: {e}")
            printer_settings_window = PlaceholderWindow(
                title="إعدادات الطابعة",
                message=f"حدث خطأ في تحميل نافذة إعدادات الطابعة:\n{str(e)}",
                parent=self
            )

        # إنشاء نافذة استيراد البيانات وتحيينها من sub1_window_html (الإصدار الحديث)
        try:
            print("INFO: محاولة تحميل نافذة استيراد البيانات وتحيينها الحديثة...")
            from sub1_window_html import ModernSub1Window
            teachers_subjects_window = ModernSub1Window(parent=self)
            
            # تحويل النافذة إلى ويدجيت مدمج
            teachers_subjects_window.setWindowFlags(Qt.Widget)
            teachers_subjects_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            
            # إزالة الـ modality للتكامل مع النافذة الرئيسية
            teachers_subjects_window.setWindowModality(Qt.NonModal)
            
            # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
            if hasattr(teachers_subjects_window, 'menuBar'):
                teachers_subjects_window.menuBar().setVisible(False)
            
            if hasattr(teachers_subjects_window, 'statusBar'):
                teachers_subjects_window.statusBar().setVisible(False)
            
            print("INFO: تم إنشاء نافذة استيراد البيانات وتحيينها من sub1_window_html (الإصدار الحديث) بنجاح")
            
        except ImportError as e:
            print(f"WARNING: فشل استيراد sub1_window_html: {e}")
            print("INFO: استخدام نافذة مؤقتة...")
            # استخدام نافذة مؤقتة
            teachers_subjects_window = PlaceholderWindow(
                title="استيراد البيانات وتحيينها",
                message="تعذر تحميل نافذة استيراد البيانات وتحيينها\n\nتأكد من وجود ملف sub1_window_html.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة استيراد البيانات وتحيينها: {e}")
            # في حالة حدوث خطأ آخر، استخدام نافذة مؤقتة
            teachers_subjects_window = PlaceholderWindow(
                title="استيراد البيانات وتحيينها",
                message=f"حدث خطأ في تحميل نافذة استيراد البيانات وتحيينها:\n{str(e)}",
                parent=self
            )


        # --- نافذة تعديل المسميات ---
        try:
            print("INFO: بدء استيراد sub6_window_html...")

            from sub6_window_html import DataEditWindow
            financial_management_window = DataEditWindow(parent=self)

            # تحويل النافذة إلى ويدجيت مدمج
            financial_management_window.setWindowFlags(Qt.Widget)
            financial_management_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

            # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
            if hasattr(financial_management_window, 'menuBar'):
                financial_management_window.menuBar().setVisible(False)

            if hasattr(financial_management_window, 'statusBar'):
                financial_management_window.statusBar().setVisible(False)

            print("INFO: تم إنشاء نافذة تعديل المسميات من sub6_window_html بنجاح")

        except ImportError as e:
            print(f"WARNING: فشل استيراد sub6_window_html: {e}")
            financial_management_window = PlaceholderWindow(
                title="تعديل المسميات",
                message="تعذر تحميل نافذة تعديل المسميات\n\nتأكد من وجود ملف sub6_window_html.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة تعديل المسميات: {e}")
            financial_management_window = PlaceholderWindow(
                title="تعديل المسميات",
                message=f"حدث خطأ في تحميل نافذة تعديل المسميات:\n{str(e)}",
                parent=self
            )

        # --- نافذة إدارة الأخبار والنشاطات ---
        try:
            print("INFO: بدء استيراد sub18_window_html...")

            from sub18_window_html import NewsWindow
            news_management_window = NewsWindow(parent=self)

            # تحويل النافذة إلى ويدجيت مدمج
            news_management_window.setWindowFlags(Qt.Widget)
            news_management_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

            # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
            if hasattr(news_management_window, 'menuBar'):
                news_management_window.menuBar().setVisible(False)

            if hasattr(news_management_window, 'statusBar'):
                news_management_window.statusBar().setVisible(False)

            print("INFO: تم إنشاء نافذة إدارة الأخبار والنشاطات من sub18_window_html بنجاح")

        except ImportError as e:
            print(f"WARNING: فشل استيراد sub18_window_html: {e}")
            news_management_window = PlaceholderWindow(
                title="إدارة الأخبار والنشاطات",
                message="تعذر تحميل نافذة إدارة الأخبار والنشاطات\n\nتأكد من وجود ملف sub18_window_html.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة إدارة الأخبار والنشاطات: {e}")
            news_management_window = PlaceholderWindow(
                title="إدارة الأخبار والنشاطات", 
                message=f"حدث خطأ في تحميل نافذة إدارة الأخبار والنشاطات:\n{str(e)}",
                parent=self
            )

        # --- نافذة أوراق التنقيط ---
        try:
            print("INFO: بدء استيراد sub37_window...")

            from sub37_window import CreateAbsenceTableWindow
            grading_sheets_window = CreateAbsenceTableWindow(
                parent=self
            )

            # تحويل النافذة إلى ويدجيت مدمج
            grading_sheets_window.setWindowFlags(Qt.Widget)
            grading_sheets_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

            # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
            if hasattr(grading_sheets_window, 'menuBar'):
                grading_sheets_window.menuBar().setVisible(False)

            if hasattr(grading_sheets_window, 'statusBar'):
                grading_sheets_window.statusBar().setVisible(False)

            print("INFO: تم إنشاء نافذة أوراق التنقيط من sub37_window بنجاح")

        except ImportError as e:
            print(f"WARNING: فشل استيراد sub37_window: {e}")
            grading_sheets_window = PlaceholderWindow(
                title="أوراق التنقيط",
                message="تعذر تحميل نافذة أوراق التنقيط\n\nتأكد من وجود ملف sub37_window.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة أوراق التنقيط: {e}")
            grading_sheets_window = PlaceholderWindow(
                title="أوراق التنقيط",
                message=f"حدث خطأ في تحميل نافذة أوراق التنقيط:\n{str(e)}",
                parent=self
            )

        # --- نافذة مسك الغياب ---
        try:
            print("INFO: بدء استيراد sub9_window...")

            from sub9_window import Hirasa300Window
            absence_management_window = Hirasa300Window(
                parent=self,
                db=self.db if hasattr(self, 'db') else None,
                academic_year=self.current_academic_year
            )

            # تحويل النافذة إلى ويدجيت مدمج
            absence_management_window.setWindowFlags(Qt.Widget)
            absence_management_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

            # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
            if hasattr(absence_management_window, 'menuBar'):
                absence_management_window.menuBar().setVisible(False)

            if hasattr(absence_management_window, 'statusBar'):
                absence_management_window.statusBar().setVisible(False)

            print("INFO: تم إنشاء نافذة مسك الغياب من sub9_window بنجاح")

        except ImportError as e:
            print(f"WARNING: فشل استيراد sub9_window: {e}")
            absence_management_window = PlaceholderWindow(
                title="مسك الغياب",
                message="تعذر تحميل نافذة مسك الغياب\n\nتأكد من وجود ملف sub9_window.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة مسك الغياب: {e}")
            absence_management_window = PlaceholderWindow(
                title="مسك الغياب",
                message=f"حدث خطأ في تحميل نافذة مسك الغياب:\n{str(e)}",
                parent=self
            )

        # --- نافذة مسك الفروض ---
        try:
            print("INFO: بدء استيراد sub21_window...")

            from sub21_window import CreateAbsenceTableWindow
            exam_management_window = CreateAbsenceTableWindow(parent=self)

            # تحويل النافذة إلى ويدجيت مدمج
            exam_management_window.setWindowFlags(Qt.Widget)
            exam_management_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

            # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
            if hasattr(exam_management_window, 'menuBar'):
                exam_management_window.menuBar().setVisible(False)

            if hasattr(exam_management_window, 'statusBar'):
                exam_management_window.statusBar().setVisible(False)

            print("INFO: تم إنشاء نافذة مسك الفروض من sub21_window بنجاح")

        except ImportError as e:
            print(f"WARNING: فشل استيراد sub21_window: {e}")
            exam_management_window = PlaceholderWindow(
                title="مسك الفروض",
                message="تعذر تحميل نافذة مسك الفروض\n\nتأكد من وجود ملف sub21_window.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة مسك الفروض: {e}")
            exam_management_window = PlaceholderWindow(
                title="مسك الفروض",
                message=f"حدث خطأ في تحميل نافذة مسك الفروض:\n{str(e)}",
                parent=self
            )

        # --- نافذة إسناد الحراسة (البنية التربوية) ---
        try:
            print("INFO: بدء استيراد sub3_window_html...")

            from sub3_window_html import SectionsManagementWindow
            educational_structure_window = SectionsManagementWindow(parent=self)

            # تحويل النافذة إلى ويدجيت مدمج
            educational_structure_window.setWindowFlags(Qt.Widget)
            educational_structure_window.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

            # إخفاء شريط القوائم وشريط الحالة إذا كانا موجودين
            if hasattr(educational_structure_window, 'menuBar'):
                educational_structure_window.menuBar().setVisible(False)

            if hasattr(educational_structure_window, 'statusBar'):
                educational_structure_window.statusBar().setVisible(False)

            print("INFO: تم إنشاء نافذة إسناد الحراسة من sub3_window_html بنجاح")

        except ImportError as e:
            print(f"WARNING: فشل استيراد sub3_window_html: {e}")
            educational_structure_window = PlaceholderWindow(
                title="إسناد الحراسة",
                message="تعذر تحميل نافذة إسناد الحراسة\n\nتأكد من وجود ملف sub3_window_html.py",
                parent=self
            )
        except Exception as e:
            print(f"ERROR: خطأ في إنشاء نافذة إسناد الحراسة: {e}")
            educational_structure_window = PlaceholderWindow(
                title="إسناد الحراسة",
                message=f"حدث خطأ في تحميل نافذة إسناد الحراسة:\n{str(e)}",
                parent=self
            )

        # جمع النوافذ في خريطة للوصول السريع
        self.windows = {
            "main_window": main_window,
            "institution_data": institution_data_window,
            "program_settings": institution_data_window,  # ربط إعدادات البرنامج بنافذة بيانات المؤسسة
            "general_statistics": general_statistics_window,
            "program_init": program_init_window,
            "teachers_subjects": teachers_subjects_window,
            "lists_sections": lists_sections_window,
            "printer_settings": printer_settings_window,  # نافذة إعدادات الطابعة
            "educational_structure": educational_structure_window,  # نافذة إسناد الحراسة
            "financial_management": financial_management_window,
            "news_management": news_management_window,  # نافذة إدارة الأخبار والنشاطات
            "grading_sheets": grading_sheets_window,  # نافذة أوراق التنقيط
            "absence_management": absence_management_window,  # نافذة مسك الغياب
            "exam_management": exam_management_window,  # نافذة مسك الفروض
        }
        
        print("INFO: بدء إضافة النوافذ إلى منطقة المحتوى...")
        self.content_area.addWidget(main_window)
        print("INFO: تم إضافة النافذة الرئيسية")
        
        self.content_area.addWidget(institution_data_window)
        print("INFO: تم إضافة نافذة بيانات المؤسسة")
        
        self.content_area.addWidget(general_statistics_window)
        print("INFO: تم إضافة نافذة الإحصائيات العامة")
        
        self.content_area.addWidget(program_init_window)
        print("INFO: تم إضافة نافذة تهيئة البرنامج")
        
        self.content_area.addWidget(teachers_subjects_window)
        print("INFO: تم إضافة نافذة استيراد البيانات وتحيينها")

        self.content_area.addWidget(lists_sections_window)
        print("INFO: تم إضافة نافذة اللوائح والأقسام")

        self.content_area.addWidget(printer_settings_window)
        print("INFO: تم إضافة نافذة إعدادات الطابعة")

        self.content_area.addWidget(educational_structure_window)
        print("INFO: تم إضافة نافذة إسناد الحراسة")

        self.content_area.addWidget(financial_management_window)
        print("INFO: تم إضافة نافذة تعديل المسميات")

        self.content_area.addWidget(news_management_window)
        print("INFO: تم إضافة نافذة إدارة الأخبار والنشاطات")

        self.content_area.addWidget(grading_sheets_window)
        print("INFO: تم إضافة نافذة أوراق التنقيط")

        self.content_area.addWidget(absence_management_window)
        print("INFO: تم إضافة نافذة مسك الغياب")

        self.content_area.addWidget(exam_management_window)
        print("INFO: تم إضافة نافذة مسك الفروض")

    def refresh_all_windows(self):
        """تحديث النوافذ - نسخة مبسطة"""
        print("تحديث النوافذ...")

    def get_unified_academic_year(self):
        """إرجاع السنة الدراسية الافتراضية"""
        return self.current_academic_year

    def show_logout_confirmation_dialog(self):
        """عرض نافذة تسجيل الخروج المحسنة مع خيارات الصيانة"""
        try:
            from PyQt5.QtWidgets import (QProgressBar, QCheckBox, QGroupBox,
                                       QTextEdit, QScrollArea, QFrame)

            # إنشاء نافذة تسجيل الخروج المحسنة
            logout_dialog = QDialog(self)
            logout_dialog.setWindowTitle("🚪 تسجيل الخروج من البرنامج")
            logout_dialog.setFixedSize(600, 500)
            logout_dialog.setLayoutDirection(Qt.RightToLeft)

            # إضافة أيقونة البرنامج
            try:
                if hasattr(self, 'app_icon') and self.app_icon:
                    logout_dialog.setWindowIcon(self.app_icon)
                else:
                    # محاولة تحميل الأيقونة مباشرة
                    icon_path = os.path.join(self.db_folder, "01.ico")
                    if os.path.exists(icon_path):
                        logout_dialog.setWindowIcon(QIcon(icon_path))
            except Exception as e:
                print(f"WARNING: لم يتم تطبيق الأيقونة على نافذة الخروج: {e}")

            # تطبيق الأنماط
            logout_dialog.setStyleSheet("""
                QDialog {
                    background-color: #f8f9fa;
                }
                QGroupBox {
                    font-weight: bold;
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    margin: 10px 0;
                    padding-top: 10px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                    background-color: #f8f9fa;
                }
                QCheckBox {
                    font-size: 12px;
                    padding: 5px;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:checked {
                    background-color: #28a745;
                    border: 2px solid #1e7e34;
                }
                QPushButton {
                    font-size: 12px;
                    font-weight: bold;
                    padding: 10px 20px;
                    border-radius: 6px;
                    margin: 5px;
                }
            """)

            main_layout = QVBoxLayout(logout_dialog)

            # العنوان الرئيسي
            title_label = QLabel("🚪 تسجيل الخروج من البرنامج")
            title_label.setFont(QFont("Calibri", 16, QFont.Bold))
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("""
                QLabel {
                    background-color: #dc3545;
                    color: white;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 10px;
                }
            """)
            main_layout.addWidget(title_label)

            # رسالة التأكيد
            message_label = QLabel("هل أنت متأكد من الخروج من البرنامج؟")
            message_label.setAlignment(Qt.AlignCenter)
            message_label.setFont(QFont("Calibri", 14))
            message_label.setStyleSheet("padding: 10px; color: #495057;")
            main_layout.addWidget(message_label)

            # مجموعة خيارات الصيانة
            maintenance_group = QGroupBox("🔧 خيارات الصيانة قبل الخروج")
            maintenance_group.setFont(QFont("Calibri", 12, QFont.Bold))
            maintenance_layout = QVBoxLayout(maintenance_group)

            # خيارات الصيانة
            self.backup_checkbox = QCheckBox("📦 عمل نسخة احتياطية من قاعدة البيانات")
            self.backup_checkbox.setChecked(True)  # مفعل افتراضياً
            maintenance_layout.addWidget(self.backup_checkbox)

            self.cleanup_checkbox = QCheckBox("🧹 تنظيف البرنامج من الرسائل التشخيصية والعمليات المحفوظة")
            self.cleanup_checkbox.setChecked(True)  # مفعل افتراضياً
            maintenance_layout.addWidget(self.cleanup_checkbox)

            self.reports_cleanup_checkbox = QCheckBox("📄 حذف جميع التقارير المؤقتة (PDF وغيرها)")
            self.reports_cleanup_checkbox.setChecked(True)  # مفعل افتراضياً
            maintenance_layout.addWidget(self.reports_cleanup_checkbox)

            self.database_optimize_checkbox = QCheckBox("⚡ ضغط وإصلاح قاعدة البيانات")
            self.database_optimize_checkbox.setChecked(True)  # مفعل افتراضياً
            maintenance_layout.addWidget(self.database_optimize_checkbox)

            main_layout.addWidget(maintenance_group)

            # منطقة عرض التقدم
            self.progress_group = QGroupBox("📊 تقدم العمليات")
            self.progress_group.setFont(QFont("Calibri", 12, QFont.Bold))
            self.progress_group.setVisible(False)  # مخفية في البداية
            progress_layout = QVBoxLayout(self.progress_group)

            self.progress_bar = QProgressBar()
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(0)
            progress_layout.addWidget(self.progress_bar)

            self.progress_label = QLabel("جاهز...")
            self.progress_label.setAlignment(Qt.AlignCenter)
            self.progress_label.setFont(QFont("Calibri", 11))
            progress_layout.addWidget(self.progress_label)

            # منطقة عرض السجل
            self.log_text = QTextEdit()
            self.log_text.setMaximumHeight(100)
            self.log_text.setFont(QFont("Consolas", 9))
            self.log_text.setStyleSheet("""
                QTextEdit {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    padding: 5px;
                }
            """)
            progress_layout.addWidget(self.log_text)

            main_layout.addWidget(self.progress_group)

            # أزرار التحكم
            button_layout = QHBoxLayout()

            # زر تنفيذ العمليات والخروج
            self.execute_exit_button = QPushButton("✅ تنفيذ العمليات والخروج")
            self.execute_exit_button.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
            self.execute_exit_button.clicked.connect(self.execute_maintenance_and_exit)
            button_layout.addWidget(self.execute_exit_button)

            # زر الإلغاء
            cancel_button = QPushButton("❌ إلغاء")
            cancel_button.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #545b62;
                }
            """)
            cancel_button.clicked.connect(logout_dialog.reject)
            button_layout.addWidget(cancel_button)

            main_layout.addLayout(button_layout)

            # حفظ مرجع للحوار
            self.logout_dialog = logout_dialog

            result = logout_dialog.exec_()
            return result == QDialog.Accepted

        except Exception as e:
            print(f"خطأ في عرض نافذة تسجيل الخروج: {e}")
            return True

    def execute_maintenance_and_exit(self):
        """تنفيذ عمليات الصيانة المختارة ثم الخروج"""
        try:
            import sqlite3
            import shutil
            import zipfile
            import tempfile
            import datetime
            import glob
            import gc
            from PyQt5.QtCore import QTimer

            # إظهار منطقة التقدم
            self.progress_group.setVisible(True)
            self.logout_dialog.setFixedSize(600, 650)  # توسيع النافذة

            # تعطيل الأزرار أثناء التنفيذ
            self.execute_exit_button.setEnabled(False)

            total_steps = 0
            current_step = 0

            # حساب عدد الخطوات المطلوبة
            if self.backup_checkbox.isChecked():
                total_steps += 1
           
            if self.cleanup_checkbox.isChecked():
                total_steps += 1
            if self.reports_cleanup_checkbox.isChecked():
                total_steps += 1
            if self.database_optimize_checkbox.isChecked():
                total_steps += 1

            if total_steps ==  0:
                self.logout_dialog.accept()
                return

            step_percentage = 100 / total_steps

            # تحديث السجل
            def log_message(message):
                self.log_text.append(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] {message}")
                self.log_text.ensureCursorVisible()
                QApplication.processEvents()

            def update_progress(step_name):
                nonlocal current_step
                current_step += 1
                progress = int(current_step * step_percentage)
                self.progress_bar.setValue(progress)
                self.progress_label.setText(f"{step_name} ({current_step}/{total_steps})")
                QApplication.processEvents()



            log_message("🚀 بدء عمليات الصيانة...")

            # 1. عمل نسخة احتياطية
            if self.backup_checkbox.isChecked():
                try:
                    update_progress("عمل نسخة احتياطية")
                    log_message("📦 بدء عمل النسخة الاحتياطية...")

                    # إنشاء مجلد النسخ الاحتياطي
                    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                    main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
                    backup_folder = os.path.join(main_folder, "النسخ الاحتياطي للبرنامج")

                    if not os.path.exists(backup_folder):
                        os.makedirs(backup_folder)

                    # توليد اسم النسخة الاحتياطية
                    current_datetime = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                    backup_name = f"database_backup_{current_datetime}"
                    backup_sqlite = os.path.join(backup_folder, f"{backup_name}.sqlite")
                    backup_zip = os.path.join(backup_folder, f"{backup_name}.zip")

                    # إنشاء النسخة الاحتياطية
                    conn = get_database_connection()
                    backup_conn = sqlite3.connect(backup_sqlite)
                    conn.backup(backup_conn)
                    backup_conn.close()
                    conn.close()

                    # ضغط النسخة الاحتياطية
                    with zipfile.ZipFile(backup_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                        zipf.write(backup_sqlite, os.path.basename(backup_sqlite))

                    # حذف الملف المؤقت
                    os.remove(backup_sqlite)

                    backup_size_kb = os.path.getsize(backup_zip) / 1024
                    size_text = f"{backup_size_kb:.2f} KB"
                    if backup_size_kb >= 1024:
                        size_text = f"{backup_size_kb/1024:.2f} MB"

                    log_message(f"✅ تم إنشاء النسخة الاحتياطية ({size_text})")

                except Exception as e:
                    log_message(f"❌ خطأ في النسخة الاحتياطية: {str(e)}")

            # 2. تنظيف الرسائل التشخيصية
            if self.cleanup_checkbox.isChecked():
                try:
                    update_progress("تنظيف الرسائل التشخيصية")
                    log_message("🧹 بدء تنظيف الرسائل التشخيصية...")

                    # تنظيف الذاكرة
                    gc.collect()

                    # تنظيف المؤشرات المتراكمة
                    for _ in range(10):
                        try:
                            QApplication.restoreOverrideCursor()
                        except:
                            break

                    # تنظيف الملفات المؤقتة
                    temp_files_cleaned = 0
                    temp_patterns = [
                        "*.tmp", "*.temp", "*.log", "*.cache",
                        "__pycache__/*", "*.pyc", "*.pyo"
                    ]

                    for pattern in temp_patterns:
                        for file_path in glob.glob(pattern):
                            try:
                                if os.path.isfile(file_path):
                                    os.remove(file_path)
                                    temp_files_cleaned += 1
                                elif os.path.isdir(file_path):
                                    shutil.rmtree(file_path, ignore_errors=True)
                                    temp_files_cleaned += 1
                            except:
                                pass

                    log_message(f"✅ تم تنظيف {temp_files_cleaned} ملف مؤقت")

                except Exception as e:
                    log_message(f"❌ خطأ في التنظيف: {str(e)}")

            # 3. حذف التقارير المؤقتة
            if self.reports_cleanup_checkbox.isChecked():
                try:
                    update_progress("حذف التقارير المؤقتة")
                    log_message("📄 بدء حذف التقارير المؤقتة...")

                    reports_cleaned = 0
                    report_patterns = [
                        "*.pdf", "تقرير_*.pdf", "report_*.pdf",
                        "تقرير_*.docx", "تقرير_*.xlsx"
                    ]

                    # حذف التقارير من المجلد الحالي
                    for pattern in report_patterns:
                        for file_path in glob.glob(pattern):
                            try:
                                if os.path.isfile(file_path):
                                    os.remove(file_path)
                                    reports_cleaned += 1
                            except:
                                pass

                    # حذف التقارير من مجلد التقارير على سطح المكتب
                    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                    reports_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")

                    if os.path.exists(reports_folder):
                        for root, dirs, files in os.walk(reports_folder):
                            for file in files:
                                if file.endswith(('.pdf', '.docx', '.xlsx')) and not file.startswith('database_backup'):
                                    try:
                                        file_path = os.path.join(root, file)
                                        os.remove(file_path)
                                        reports_cleaned += 1
                                    except:
                                        pass

                    log_message(f"✅ تم حذف {reports_cleaned} تقرير مؤقت")

                except Exception as e:
                    log_message(f"❌ خطأ في حذف التقارير: {str(e)}")

            # 4. ضغط وإصلاح قاعدة البيانات
            if self.database_optimize_checkbox.isChecked():
                try:
                    update_progress("ضغط وإصلاح قاعدة البيانات")
                    log_message("⚡ بدء ضغط وإصلاح قاعدة البيانات...")

                    # الحصول على حجم قاعدة البيانات قبل الضغط
                    original_size = os.path.getsize(self.db_path) / 1024  # KB

                    # فتح اتصال بقاعدة البيانات
                    conn = get_database_connection()

                    # فحص سلامة قاعدة البيانات
                    cursor = conn.cursor()
                    cursor.execute("PRAGMA integrity_check")
                    integrity_result = cursor.fetchone()[0]

                    if integrity_result == "ok":
                        log_message("✅ فحص سلامة قاعدة البيانات: ناجح")
                    else:
                        log_message(f"⚠️ فحص سلامة قاعدة البيانات: {integrity_result}")

                    # ضغط قاعدة البيانات
                    cursor.execute("VACUUM")
                    conn.commit()
                    conn.close()

                    # الحصول على حجم قاعدة البيانات بعد الضغط
                    new_size = os.path.getsize(self.db_path) / 1024  # KB
                    saved_space = original_size - new_size

                    log_message(f"✅ تم ضغط قاعدة البيانات")
                    log_message(f"📊 الحجم الأصلي: {original_size:.2f} KB")
                    log_message(f"📊 الحجم الجديد: {new_size:.2f} KB")
                    log_message(f"💾 المساحة المحفوظة: {saved_space:.2f} KB")

                except Exception as e:
                    log_message(f"❌ خطأ في ضغط قاعدة البيانات: {str(e)}")

            # إكمال العمليات
            self.progress_bar.setValue(100)
            self.progress_label.setText("✅ تم إكمال جميع العمليات بنجاح")
            log_message("🎉 تم إكمال جميع عمليات الصيانة بنجاح!")
            log_message("🚪 جاهز للخروج من البرنامج...")

            # انتظار قصير لعرض النتائج
            QTimer.singleShot(2000, self.logout_dialog.accept)

        except Exception as e:
            if hasattr(self, 'log_text'):
                self.log_text.append(f"❌ خطأ عام في عمليات الصيانة: {str(e)}")
            print(f"خطأ في عمليات الصيانة: {e}")
            self.logout_dialog.accept()

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        try:
            should_quit = self.show_logout_confirmation_dialog()
            if not should_quit:
                event.ignore()
                return

            # تنظيف النوافذ المفتوحة
            try:
                # إغلاق نافذة سجلات الدخول والتأخر
                if hasattr(self, 'entry_records_window') and self.entry_records_window:
                    self.entry_records_window.close()
                    print("INFO: تم إغلاق نافذة سجلات الدخول والتأخر")

                # إغلاق نافذة سجلات المخالفات
                if hasattr(self, 'violations_window') and self.violations_window:
                    self.violations_window.close()
                    print("INFO: تم إغلاق نافذة سجلات المخالفات")
            except Exception as cleanup_error:
                print(f"WARNING: خطأ في تنظيف النوافذ المفتوحة: {cleanup_error}")

            # تنظيف نهائي قبل الإغلاق
            try:
                # تنظيف المؤشرات المتراكمة
                for _ in range(5):
                    try:
                        QApplication.restoreOverrideCursor()
                    except:
                        break

                # إغلاق اتصالات قاعدة البيانات
                if hasattr(self, 'db') and self.db:
                    self.db.close()

                if hasattr(self, 'qsql_db') and self.qsql_db.isOpen():
                    self.qsql_db.close()

                # تنظيف الذاكرة
                import gc
                gc.collect()

            except:
                pass

            event.accept()
        except Exception as e:
            print(f"خطأ أثناء معالجة إغلاق النافذة: {e}")
            event.accept()


if __name__ == "__main__":
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = MainWindow(auto_show=True)
    window.show()
    exit_code = app.exec_()
    sys.exit(exit_code)
