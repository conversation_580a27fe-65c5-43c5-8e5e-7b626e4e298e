#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
محسن الأداء - performance_optimizer.py
ملف مخصص لتحسين أداء التطبيق وتقليل الومضات
"""

import os
import sys
from PyQt5.QtCore import Qt, QCoreApplication

class PerformanceOptimizer:
    """فئة لتحسين أداء التطبيق وتقليل الومضات"""
    
    @staticmethod
    def setup_qt_environment():
        """إعداد بيئة Qt لتحسين الأداء"""
        # قمع التحذيرات غير المهمة
        os.environ['QT_LOGGING_RULES'] = (
            '*.debug=false;'
            'qt.qpa.*=false;'
            'qt.webenginecontext.debug=false;'
            'qt.webengine.*=false;'
            'qt.webchannel.*=false;'
            'qt.network.ssl.debug=false;'
            'qt.quick.*=false;'
            'qt.accessibility.*=false;'
            'qt.scenegraph.*=false'
        )
        
        # تحسينات الأداء
        QCoreApplication.setAttribute(Qt.AA_ShareOpenGLContexts)
        QCoreApplication.setAttribute(Qt.AA_UseHighDpiPixmaps)
        QCoreApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
        
    @staticmethod
    def optimize_window_creation(window):
        """تحسين إنشاء النوافذ لتقليل الومضات"""
        if window:
            # تعطيل التحديثات أثناء التهيئة
            window.setUpdatesEnabled(False)
            
            # عدم الإظهار أثناء التهيئة
            window.setAttribute(Qt.WA_DontShowOnScreen, True)
            
            # تحسينات إضافية
            window.setAttribute(Qt.WA_OpaquePaintEvent, True)
            window.setAttribute(Qt.WA_NoSystemBackground, False)
            
    @staticmethod
    def finalize_window_creation(window):
        """إنهاء تحسين النافذة وإعادة تفعيل التحديثات"""
        if window:
            # إعادة تفعيل التحديثات
            window.setAttribute(Qt.WA_DontShowOnScreen, False)
            window.setUpdatesEnabled(True)
            
            # تحديث النافذة
            window.update()
            
    @staticmethod
    def suppress_debug_messages():
        """قمع رسائل التشخيص غير المهمة"""
        # إعادة توجيه stderr لتقليل الرسائل
        if not sys.stderr.isatty():
            sys.stderr = open(os.devnull, 'w')
            
    @staticmethod
    def optimize_database_logging():
        """تحسين رسائل قاعدة البيانات"""
        # يمكن استخدام هذه الدالة لتقليل رسائل قاعدة البيانات
        pass
        
    @staticmethod
    def apply_all_optimizations(window=None):
        """تطبيق جميع التحسينات"""
        PerformanceOptimizer.setup_qt_environment()
        PerformanceOptimizer.suppress_debug_messages()
        
        if window:
            PerformanceOptimizer.optimize_window_creation(window)
            
        return True

# دالة مساعدة للاستخدام السريع
def optimize_app_performance(window=None):
    """دالة مساعدة لتطبيق جميع التحسينات بسرعة"""
    return PerformanceOptimizer.apply_all_optimizations(window)

def finalize_window(window):
    """دالة مساعدة لإنهاء تحسين النافذة"""
    return PerformanceOptimizer.finalize_window_creation(window)
