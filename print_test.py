# -*- coding: utf-8 -*-
"""
ملف الطباعة المحسن - print_test.py
======================================

تم تحديث هذا الملف بخصائص الطباعة الحرارية المستخرجة من:
taheri10/monthly_duties_window.py

الخصائص المطبقة:
- حجم الورق: 75mm x 170mm (مناسب للطابعات الحرارية 80mm)
- الهوامش: 0.4mm من كل الجهات
- الخط: Calibri 12pt Bold
- المحاذاة: وسط للعناوين، يمين للمحتوى
- تنسيق محسن للجداول والبيانات
- طباعة مباشرة باستخدام QPainter و QPrinter
"""

import os
import tempfile
import sqlite3
import time
from datetime import datetime
from database_config import get_database_path, get_database_connection

def draw_table(painter, table_data, start_y, page_width, line_height, font_metrics):
    """رسم جدول حقيقي بخطوط وحدود مع رأس الجدول ومحاذاة يمين"""
    try:
        from PyQt5.QtGui import QPen
        from PyQt5.QtCore import QRect, Qt

        if not table_data:
            return start_y

        # إعدادات الجدول - محاذاة يمين
        col1_width = int(page_width * 0.7)  # 70% للاسم (العمود الأول)
        col2_width = int(page_width * 0.3)  # 30% للرقم الترتيبي (العمود الثاني)
        table_x = 10
        table_width = col1_width + col2_width
        row_height = line_height + 6  # ارتفاع أكبر للصفوف

        # حساب ارتفاع الجدول الكامل (بيانات + رأس الجدول)
        total_rows = len(table_data) + 1  # +1 لرأس الجدول
        table_height = total_rows * row_height

        # رسم الإطار الخارجي للجدول
        painter.setPen(QPen(Qt.black, 2))  # خط أسود سميك
        table_rect = QRect(table_x, int(start_y), table_width, table_height)
        painter.drawRect(table_rect)

        # رسم الخط الفاصل بين العمودين
        painter.setPen(QPen(Qt.black, 1))  # خط أسود عادي
        separator_x = table_x + col1_width
        painter.drawLine(separator_x, int(start_y), separator_x, int(start_y + table_height))

        # رسم الخطوط الأفقية بين الصفوف (بما في ذلك خط تحت رأس الجدول)
        for i in range(1, total_rows):
            y_line = int(start_y + i * row_height)
            painter.drawLine(table_x, y_line, table_x + table_width, y_line)

        # رسم النصوص في الجدول
        painter.setPen(Qt.black)  # لون النص

        # رسم رأس الجدول أولاً
        header_y = int(start_y)
        header_rect_name = QRect(table_x + 5, header_y + 2, col1_width - 10, row_height - 4)
        header_rect_rt = QRect(separator_x + 5, header_y + 2, col2_width - 10, row_height - 4)
        
        # رسم عناوين الأعمدة
        painter.drawText(header_rect_name, Qt.AlignCenter | Qt.AlignVCenter, "الاسم الكامل")
        painter.drawText(header_rect_rt, Qt.AlignCenter | Qt.AlignVCenter, "رت")

        # رسم بيانات الطلاب
        for i, (name, rt_number) in enumerate(table_data):
            row_y = int(start_y + (i + 1) * row_height)  # +1 لتخطي رأس الجدول

            # رسم الاسم في العمود الأول (محاذاة يمين)
            name_rect = QRect(table_x + 5, row_y + 2, col1_width - 10, row_height - 4)
            painter.drawText(name_rect, Qt.AlignRight | Qt.AlignVCenter, name)

            # رسم الرقم الترتيبي في العمود الثاني (محاذاة وسط)
            rt_rect = QRect(separator_x + 5, row_y + 2, col2_width - 10, row_height - 4)
            painter.drawText(rt_rect, Qt.AlignCenter | Qt.AlignVCenter, rt_number)

        # إرجاع الموضع Y الجديد بعد الجدول
        return start_y + table_height + line_height

    except Exception as e:
        print(f"❌ [ERROR] خطأ في رسم الجدول: {str(e)}")
        return start_y

def print_directly_to_thermal_printer(printer_name, content):
    """
    طباعة مباشرة على الطابعة الحرارية بتقنية PyQt5 مع جدول حقيقي
    
    المحاذاة والتنسيق:
    - العناوين الرئيسية: وسط الصفحة
    - النص العادي: محاذاة لليمين (RTL)
    - الجداول: جدول حقيقي بخطوط وحدود مع محاذاة يمين
    - معلومات القسم والتاريخ: وسط الصفحة
    - الأعمدة: الاسم (70%) + الرقم الترتيبي (30%)
    """
    try:
        from PyQt5.QtPrintSupport import QPrinter
        from PyQt5.QtGui import QPainter, QFont, QFontMetrics, QPen
        from PyQt5.QtCore import QSizeF, QRect, Qt

        # إعداد الطابعة
        printer = QPrinter()
        printer.setPrinterName(printer_name)

        # إعداد حجم الورقة للطابعة الحرارية (80mm)
        printer.setPageSize(QPrinter.Custom)
        printer.setPageSizeMM(QSizeF(75, 170))  # عرض 75 ارتفاع 170

        # إعداد الهوامش - 0.4 من كل الجهات
        printer.setPageMargins(0.4, 0.4, 0.4, 0.4, QPrinter.Millimeter)

        # إعداد الرسام
        painter = QPainter()
        if painter.begin(printer):
            # إعداد الخط Calibri 12 غامق
            font = QFont("Calibri", 12, QFont.Bold)
            painter.setFont(font)
            painter.setPen(Qt.black)

            # حساب مقاييس الخط
            font_metrics = QFontMetrics(font)
            line_height = font_metrics.height() + 4  # إضافة مسافة بين الأسطر

            # تقسيم المحتوى إلى أسطر
            lines = content.split('\n')

            # إعداد الرسم
            y_position = 20  # البدء من الأعلى
            page_width = printer.pageRect().width() - 20  # عرض الصفحة مع الهوامش

            # رسم المحتوى مع استخدام جدول حقيقي
            y_position = 20  # البدء من الأعلى
            table_data = []  # بيانات الجدول
            in_table_section = False
            table_start_y = None

            for line in lines:
                if line.strip():  # تجاهل الأسطر الفارغة
                    # التحقق من خطوط الزخرفة أولاً
                    if line.strip().startswith('='):
                        # رسم الخطوط المزخرفة بعرض الجدول (مثل الملف المرجعي)
                        # حساب عدد الرموز المطلوبة لملء عرض الجدول
                        char_width = font_metrics.width('=')
                        num_chars = int(page_width / char_width)
                        decorative_line = '=' * num_chars

                        # رسم الخط المزخرف بنفس موضع الجدول
                        painter.drawText(10, int(y_position), decorative_line)
                        y_position += line_height
                        continue
                        
                    # تجاهل الخطوط المتقطعة
                    elif line.strip().startswith('-'):
                        continue
                        
                    # تحديد نوع المحاذاة حسب المحتوى - مثل الملف المرجعي
                    elif (any(keyword in line for keyword in ['ورقة', 'السنة الدراسية', 'الرمز السري', 'كلية', 'جامعة', 'المعهد']) or
                          any(keyword in line for keyword in ['اسماء', 'معهد', 'مؤسسة', 'مدرسة', 'أكاديمية', 'ثانوية', 'إعدادية', 'ابتدائية']) or
                          'وصل الأداء' in line or 'وصل التسجيل' in line or 'شكراً لكم' in line):
                        # إذا كنا في قسم الجدول، ارسم الجدول أولاً
                        if table_data and table_start_y is not None:
                            y_position = draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)
                            table_data = []
                            table_start_y = None
                            in_table_section = False
                        
                        # رسم البيانات الأولية والعناوين في الوسط
                        text_width = font_metrics.width(line)
                        x_position = int((page_width - text_width) / 2 + 10)
                        x_position = max(10, x_position)
                        painter.drawText(x_position, int(y_position), line)
                        y_position += line_height
                        
                    elif 'من قســـــم' in line or 'التاريخ:' in line:
                        # إذا كنا في قسم الجدول، ارسم الجدول أولاً
                        if table_data and table_start_y is not None:
                            y_position = draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)
                            table_data = []
                            table_start_y = None
                            in_table_section = False
                        
                        # رسم معلومات القسم والتاريخ في الوسط
                        text_width = font_metrics.width(line)
                        x_position = int((page_width - text_width) / 2 + 10)
                        x_position = max(10, x_position)
                        painter.drawText(x_position, int(y_position), line)
                        y_position += line_height
                        
                    elif line.startswith('رت\t') or 'الاسم الكامل' in line:
                        # بداية قسم الجدول
                        if not in_table_section:
                            table_start_y = y_position
                            in_table_section = True
                        # تجاهل رأس الجدول - سيتم رسمه مع الجدول
                        continue
                        
                    elif '\t' in line and any(char.isdigit() for char in line[:5]):
                        # جمع بيانات الطلاب للجدول
                        if not in_table_section:
                            table_start_y = y_position
                            in_table_section = True
                            
                        parts = line.split('\t')
                        if len(parts) >= 2:
                            rt_number = parts[0].strip()
                            student_name = parts[1].strip()
                            table_data.append((student_name, rt_number))
                        
                    else:
                        # إذا كنا في قسم الجدول، ارسم الجدول أولاً
                        if table_data and table_start_y is not None:
                            y_position = draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)
                            table_data = []
                            table_start_y = None
                            in_table_section = False
                        
                        # فحص إذا كان النص يحتوي على اسم المؤسسة
                        if (line.startswith('--') and any(keyword in line for keyword in ['اسماء', 'معهد', 'مؤسسة', 'مدرسة', 'أكاديمية', 'ثانوية', 'إعدادية', 'ابتدائية'])) or any(keyword in line for keyword in ['اسماء', 'معهد', 'مؤسسة', 'مدرسة', 'أكاديمية', 'ثانوية', 'إعدادية', 'ابتدائية']):
                            # رسم اسم المؤسسة في الوسط
                            text_width = font_metrics.width(line)
                            x_position = int((page_width - text_width) / 2 + 10)
                            x_position = max(10, x_position)
                            painter.drawText(x_position, int(y_position), line)
                            y_position += line_height
                        else:
                            # رسم النص العادي محاذاة لليمين
                            text_width = font_metrics.width(line)
                            x_position = int(page_width - text_width + 10)
                            x_position = max(10, x_position)
                            painter.drawText(x_position, int(y_position), line)
                            y_position += line_height

            # رسم الجدول الأخير إذا كان متبقي
            if table_data and table_start_y is not None:
                y_position = draw_table(painter, table_data, table_start_y, page_width, line_height, font_metrics)

            painter.end()
            print(f"✅ [SUCCESS] تم إرسال الطباعة الحرارية على: {printer_name}")
            return True
        else:
            raise Exception(f"فشل في بدء الطباعة على الطابعة: {printer_name}")

    except Exception as e:
        print(f"❌ [ERROR] خطأ في الطباعة الحرارية المباشرة: {str(e)}")
        return False

def thermal_print(content):
    """طباعة حرارية محسنة باستخدام PyQt5"""
    printer_name = get_printer_name()

    if not printer_name:
        print("لم يتم العثور على طابعة متاحة")
        return False

    # جرب الطباعة الحرارية المباشرة أولاً
    try:
        return print_directly_to_thermal_printer(printer_name, content)
    except ImportError:
        print("PyQt5 غير متوفر، استخدام الطريقة القديمة...")
        return direct_print(content)
    except Exception as e:
        print(f"فشل في الطباعة الحرارية المباشرة، استخدام الطريقة القديمة: {e}")
        return direct_print(content)

# عرض كامل معلومات قاعدة البيانات للتشخيص
def print_db_info():
    """طباعة معلومات قاعدة البيانات للتشخيص"""
    try:
        conn = sqlite3.connect(get_database_path())
        cursor = conn.cursor()

        # عرض جميع الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print("جداول قاعدة البيانات:")
        for table in tables:
            print(f"  - {table[0]}")

        # عرض هيكل جدول app_settings إذا كان موجودًا
        if ('app_settings',) in tables:
            cursor.execute("PRAGMA table_info(app_settings)")
            columns = cursor.fetchall()
            print("\nهيكل جدول app_settings:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")

            # عرض محتويات جدول app_settings
            cursor.execute("SELECT * FROM app_settings")
            rows = cursor.fetchall()
            print("\nمحتويات جدول app_settings:")
            for row in rows:
                print(f"  - {row}")

        # عرض بيانات_المؤسسة إذا كان موجودًا
        if ('بيانات_المؤسسة',) in tables:
            cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
            columns = cursor.fetchall()
            print("\nهيكل جدول بيانات_المؤسسة:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")

            cursor.execute("SELECT * FROM بيانات_المؤسسة")
            rows = cursor.fetchall()
            print("\nمحتويات جدول بيانات_المؤسسة:")
            for row in rows:
                print(f"  - {row}")

        conn.close()
    except Exception as e:
        print(f"خطأ في طباعة معلومات قاعدة البيانات: {e}")

def get_printer_name():
    """الحصول على اسم الطابعة من قاعدة البيانات أو الافتراضية"""
    try:
        conn = sqlite3.connect(get_database_path())
        cursor = conn.cursor()

        # محاولة الحصول على الطابعة من جدول إعدادات_الطابعة
        cursor.execute("SELECT الطابعة_الحرارية FROM إعدادات_الطابعة LIMIT 1")
        result = cursor.fetchone()
        conn.close()

        if result and result[0]:
            return result[0]
    except:
        pass

    # الحصول على الطابعة الافتراضية إذا لم تكن موجودة في قاعدة البيانات
    try:
        if os.name == 'nt':  # Windows
            import win32print
            return win32print.GetDefaultPrinter()
    except:
        pass

    return None

def direct_print(content):
    """طباعة المحتوى مباشرة"""
    printer_name = get_printer_name()

    if not printer_name:
        print("لم يتم العثور على طابعة متاحة")
        return False

    # إنشاء ملف مؤقت
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".txt", mode="w", encoding="utf-8") as f:
            f.write(content)
            file_path = f.name

        print(f"تم إنشاء ملف مؤقت: {file_path}")
        print(f"محتوى الملف:\n{content}")

        success = False

        # طباعة الملف باستخدام الطريقة المناسبة
        if os.name == 'nt':  # Windows
            try:
                # طريقة win32api
                import win32api
                win32api.ShellExecute(0, "printto", file_path, f'"{printer_name}"', ".", 0)
                print(f"تم إرسال الطباعة إلى {printer_name} باستخدام win32api")
                success = True
            except ImportError:
                try:
                    # طريقة print command
                    import subprocess
                    subprocess.run(f'print /d:"{printer_name}" "{file_path}"', shell=True)
                    print(f"تم إرسال الطباعة باستخدام أمر print")
                    success = True
                except Exception as e:
                    try:
                        # طريقة startfile
                        os.startfile(file_path, "print")
                        print("تم إرسال الطباعة باستخدام startfile")
                        success = True
                    except Exception as e2:
                        print(f"خطأ في الطباعة: {e2}")
        else:  # Linux/Mac
            try:
                import subprocess
                subprocess.run(["lp", "-d", printer_name, file_path])
                print(f"تم إرسال الطباعة باستخدام lp")
                success = True
            except Exception as e:
                print(f"خطأ في الطباعة: {e}")

        # الانتظار وحذف الملف المؤقت
        time.sleep(2)
        try:
            os.unlink(file_path)
            print("تم حذف الملف المؤقت")
        except:
            print("لم يمكن حذف الملف المؤقت")

        return success

    except Exception as e:
        print(f"خطأ عام: {e}")
        return False

def get_form_info(form_id=1):
    """الحصول على معلومات النموذج من قاعدة البيانات
    form_id: 1 = ورقة الدخول، 2 = ورقة التأخر
    """
    # القيم الافتراضية
    institution_name = "المؤسسة التعليمية"
    school_year = "2024/2025"
    form_title = "ورقة الدخول" if form_id == 1 else "ورقة التأخر"  # قيمة افتراضية حسب النوع
    form_description = ""  # قيمة افتراضية
    form_description2 = ""  # سطر ثاني للملاحظات

    try:
        conn = sqlite3.connect(get_database_path())
        cursor = conn.cursor()

        # 1. استخراج اسم المؤسسة والسنة الدراسية
        cursor.execute("SELECT المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        if result:
            institution_name = result[0] or institution_name
            school_year = result[1] or school_year

        # 2. استخراج عنوان النموذج وملاحظاته من جدول تعديل_المسميات
        try:
            cursor.execute("""
                SELECT العنوان, ملاحظات
                FROM تعديل_المسميات
                WHERE id = ?
            """, (form_id,))
            result = cursor.fetchone()
            if result:
                if result[0]:  # العنوان
                    form_title = result[0]
                    print(f"تم استخراج عنوان النموذج (ID {form_id}): {form_title}")
                if result[1]:  # الملاحظات من قاعدة البيانات
                    # تقسيم النص من قاعدة البيانات: 5 كلمات في السطر الأول والباقي في الثاني
                    full_notes = result[1].strip()
                    print(f"تم استخراج الملاحظات من قاعدة البيانات (ID {form_id}): {full_notes}")

                    if '\n' in full_notes:
                        # إذا كان هناك سطر جديد في قاعدة البيانات، استخدمه
                        notes = full_notes.split('\n', 1)
                        form_description = notes[0].strip()
                        form_description2 = notes[1].strip() if len(notes) > 1 else ""
                    else:
                        # تقسيم النص إلى كلمات
                        words = full_notes.split()

                        if len(words) > 5:
                            # أول 5 كلمات في السطر الأول
                            form_description = ' '.join(words[:5])
                            # باقي الكلمات في السطر الثاني
                            form_description2 = ' '.join(words[5:])
                        else:
                            # إذا كان النص 5 كلمات أو أقل، يبقى في سطر واحد
                            form_description = full_notes
                            form_description2 = ""

                    print(f"السطر الأول من الملاحظات (5 كلمات): {form_description}")
                    if form_description2:
                        print(f"السطر الثاني من الملاحظات (الباقي): {form_description2}")
                else:
                    print(f"لا توجد ملاحظات في قاعدة البيانات للسجل رقم {form_id}")
            else:
                print(f"لم يتم العثور على بيانات في جدول تعديل_المسميات للسجل رقم {form_id}")
        except Exception as e:
            print(f"خطأ عند استخراج البيانات من جدول تعديل_المسميات: {e}")

        conn.close()

    except Exception as e:
        print(f"خطأ عام في استخراج معلومات النموذج: {e}")

    return institution_name, school_year, form_title, form_description, form_description2

def print_secret_code_form(students, date_str=None):
    """طباعة نموذج الرمز السري
    students: قائمة التلاميذ مع الرموز السرية
    date_str: التاريخ (اختياري)
    """
    # تحديد التاريخ إذا لم يتم تمريره
    if not date_str:
        date_str = datetime.now().strftime("%Y/%m/%d")

    # الحصول على معلومات المؤسسة
    institution_name, school_year, _, _, _ = get_form_info(1)  # استخدام معلومات عامة

    # طباعة معلومات التشخيص
    print("\nمعلومات نموذج الرمز السري:")
    print(f"اسم المؤسسة: {institution_name}")
    print(f"السنة الدراسية: {school_year}")
    print(f"عدد التلاميذ: {len(students)}")

    # إنشاء محتوى الطباعة لكل تلميذ منفصل
    for student in students:
        content = f"{institution_name}\n"
        content += f"السنة الدراسية: {school_year}\n"
        content += f"الرمز السري\n"
        content += "----------------------------------------------\n\n"

        content += f"رت: {student.get('rt', '')}\n"
        content += f"رمز التلميذ: {student.get('code', '')}\n"
        content += f"الاسم والنسب: {student.get('name', '')}\n"
        content += f"اسم المستخدم: {student.get('code', '')}@taalim.ma\n"
        content += f"الرمز السري: {student.get('secret', 'غير متوفر')}\n\n"
        content += "----------------------------------------------\n"
        content += f"التاريخ: {date_str}\n\n\n\n"

        # طباعة المحتوى باستخدام الطباعة الحرارية المحسنة لكل تلميذ
        print(f"طباعة الرمز السري للتلميذ: {student.get('name', '')}")
        success = thermal_print(content)
        if not success:
            print(f"فشلت طباعة الرمز السري للتلميذ: {student.get('name', '')}")
            return False

    return True

def print_entry_form(students, section, date_str=None, time_str=None, form_type="entry", form_id=None):
    """طباعة نموذج الدخول أو التأخر أو التوجيه أو الاستئذان
    form_type: "entry" = ورقة الدخول، "late" = ورقة التأخر، "guidance" = ورقة التوجيه، "permission" = ورقة الاستئذان
    form_id: معرف النموذج في قاعدة البيانات (1=دخول، 2=تأخر، 3=توجيه، 4=استئذان)
    """
    # تحديد التاريخ والوقت إذا لم يتم تمريرهما
    if not date_str:
        date_str = datetime.now().strftime("%Y/%m/%d")
    if not time_str:
        time_str = datetime.now().strftime("%H:%M")

    # تحديد معرف النموذج حسب النوع إذا لم يتم تمريره
    if form_id is None:
        if form_type == "entry":
            form_id = 1
        elif form_type == "late":
            form_id = 2
        elif form_type == "guidance":
            form_id = 3
        elif form_type == "permission":
            form_id = 4
        else:
            form_id = 1  # افتراضي

    # الحصول على معلومات النموذج
    institution_name, school_year, form_title, form_description, form_description2 = get_form_info(form_id)

    # طباعة معلومات التشخيص
    print("\nمعلومات النموذج:")
    print(f"اسم المؤسسة: {institution_name}")
    print(f"السنة الدراسية: {school_year}")
    print(f"عنوان النموذج: {form_title}")
    print(f"وصف النموذج: {form_description}")

    # الملاحظات ستتم إضافتها مقسمة على سطرين منفصلين

    # إنشاء محتوى الطباعة مع خطوط الزخرفة
    content = "============================\n"
    content += f"{institution_name}\n"
    content += "============================\n"
    content += f"السنة الدراسية: {school_year}\n"
    content += f"{form_title}\n"

    # إضافة الملاحظات مقسمة على سطرين
    if form_description:
        content += f"{form_description}\n"
    if form_description2:
        content += f"{form_description2}\n"

    # بدء قسم الجدول - رأس الجدول سيتم رسمه تلقائياً في دالة draw_table

    # إضافة بيانات الطلاب
    for student in students:
        if isinstance(student, dict) and 'rt' in student and 'name' in student:
            content += f"{student['rt']}\t           {student['name']}\n"
        else:
            content += f"{student}\n"

    # إنهاء قسم الجدول ومعلومات إضافية
    content += f"من قســـــم : {section}\n"
    content += f"التاريخ: {date_str}  الوقت: {time_str}\n\n\n\n"

    # طباعة المحتوى باستخدام الطباعة الحرارية المحسنة
    return thermal_print(content)

def get_doctor_visit_form_info():
    """الحصول على معلومات نموذج زيارة الطبيب من قاعدة البيانات"""
    institution_name = "المؤسسة التعليمية"
    school_year = "2024/2025"
    form_title = "ورقة زيارة الطبيب"  # قيمة افتراضية
    form_description = ""  # قيمة افتراضية

    try:
        conn = sqlite3.connect(get_database_path())
        cursor = conn.cursor()

        # 1. استخراج اسم المؤسسة والسنة الدراسية
        cursor.execute("SELECT المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        if result:
            institution_name = result[0] or institution_name
            school_year = result[1] or school_year

        # 2. استخراج عنوان نموذج زيارة الطبيب وملاحظاته من جدول تعديل_المسميات
        try:
            cursor.execute("""
                SELECT العنوان, ملاحظات
                FROM تعديل_المسميات
                WHERE id = 5
            """)
            result = cursor.fetchone()
            if result:
                if result[0]:  # العنوان
                    form_title = result[0]
                if result[1]:  # الملاحظات
                    form_description = result[1]
        except Exception as e:
            print(f"خطأ عند استخراج معلومات نموذج زيارة الطبيب: {e}")

        conn.close()
    except Exception as e:
        print(f"خطأ في استخراج معلومات نموذج زيارة الطبيب: {e}")

    return institution_name, school_year, form_title, form_description

def print_doctor_visit_form(students, section=None, date_str=None, time_str=None):
    """طباعة نموذج زيارة الطبيب مع زيادة المسافة بين الأسطر في منطقة رأي الطبيب"""
    try:
        from PyQt5.QtPrintSupport import QPrinter
        from PyQt5.QtGui import QTextDocument, QPageSize
        from PyQt5.QtCore import QSizeF, Qt, QMarginsF, QDateTime
        from PyQt5.QtWidgets import QFileDialog
        import os
        
        # الحصول على بيانات المؤسسة من قاعدة البيانات
        school_info = get_school_info()
        if not school_info:
            print("لم يتم العثور على بيانات المؤسسة")
            return False
        
        # تحديد التاريخ والوقت الحاليين إذا لم يتم تمريرهما
        if not date_str:
            date_str = QDateTime.currentDateTime().toString("yyyy-MM-dd")
        if not time_str:
            time_str = QDateTime.currentDateTime().toString("hh:mm")
        
        # التأكد من وجود طلاب محددين
        if not students or len(students) == 0:
            print("لا يوجد طلاب محددين لطباعة نموذج زيارة الطبيب")
            return False
        
        for student in students:
            # إنشاء محتوى HTML للنموذج
            document = QTextDocument()
            document.setDocumentMargin(10)
            
            # تعيين حجم الصفحة إلى A5
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPageSize(QPageSize.A5))
            printer.setPageOrientation(QPrinter.Portrait)
            
            # تعيين الهوامش
            margins = QMarginsF(10, 10, 10, 10)  # يسار، أعلى، يمين، أسفل
            printer.setPageMargins(margins, QPrinter.Point)
            
            # HTML للنموذج مع زيادة المسافة بين الأسطر في منطقة رأي الطبيب
            html_content = f"""
            <html dir="rtl">
            <head>
                <style>
                    body {{
                        font-family: Calibri, Arial;
                        font-size: 10pt;
                        direction: rtl;
                        padding: 0;
                        margin: 0;
                    }}
                    table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 5px;
                    }}
                    table, th, td {{
                        border: 1px solid black;
                    }}
                    th, td {{
                        padding: 4px;
                        text-align: right;
                    }}
                    .header {{
                        text-align: center;
                        font-weight: bold;
                        margin: 5px 0;
                    }}
                    .signature {{
                        text-align: center;
                        margin-top: 10px;
                    }}
                    /* زيادة المسافات بين أسطر رأي الطبيب */
                    .doctor-opinion {{
                        line-height: 2.0; /* زيادة المسافة بين الأسطر */
                    }}
                </style>
            </head>
            <body>
                <div class="header">
                    <p style="font-size: 11pt; margin: 0;">السنة الدراسية : {school_info.get('academic_year', '')}</p>
                </div>
                <div class="header">
                    <p style="font-size: 12pt; margin: 5px 0;">{school_info.get('school_name', '')}</p>
                    <p style="font-size: 11pt; margin: 0;">ورقة زيارة الطبيب</p>
                </div>
                
                <table>
                    <tr>
                        <td colspan="2" style="text-align: right;">يسمح للتلميذ(ة) : {student.get('name', '')}</td>
                    </tr>
                    <tr>
                        <td style="text-align: right;">قسم : {section}</td>
                        <td style="text-align: right;">رقم التسجيل : {student.get('code', '')}</td>
                    </tr>
                    <tr>
                        <td style="text-align: right;">بزيارة الطبيب يوم : {date_str}</td>
                        <td style="text-align: right;">على الساعة : {time_str}</td>
                    </tr>
                </table>
                
                <div style="display: flex; margin-top: 10px;">
                    <div style="width: 50%; text-align: right;">
                        <p>رأي الطبيب</p>
                        <!-- إضافة الأسطر مع زيادة المسافة بينها -->
                        <div class="doctor-opinion">
                            <hr style="margin: 8px 0;">
                            <hr style="margin: 8px 0;">
                            <hr style="margin: 8px 0;">
                            <hr style="margin: 8px 0;">
                            <hr style="margin: 8px 0;">
                            <hr style="margin: 8px 0;">
                        </div>
                    </div>
                    
                    <div style="width: 50%; text-align: right;">
                        <p>ختم وتوقيع الحراسة العامة</p>
                    </div>
                </div>
                
                <div style="text-align: right; margin-top: 10px;">
                    <p>توقيع الطبيب</p>
                </div>
            </body>
            </html>
            """
            
            document.setHtml(html_content)
            
            # تعيين حجم المستند ليتناسب مع حجم الصفحة
            document.setPageSize(QSizeF(printer.pageRect(QPrinter.Point).size()))
            
            # معاينة وطباعة النموذج
            fileName, _ = QFileDialog.getSaveFileName(None, "حفظ كملف PDF", 
                                                     os.path.join(os.path.expanduser("~"), "Desktop", f"زيارة_طبيب_{student.get('name', '')}.pdf"), 
                                                     "PDF Files (*.pdf)")
            if fileName:
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(fileName)
                document.print_(printer)
                print(f"تم حفظ نموذج زيارة الطبيب بنجاح كملف PDF: {fileName}")
                return True
            else:
                print("تم إلغاء الحفظ من قبل المستخدم")
                return False
        
        return True
    except Exception as e:
        print(f"حدث خطأ أثناء طباعة نموذج زيارة الطبيب: {e}")
        return False

# وظيفة للحصول على بيانات المؤسسة
def get_school_info():
    try:
        import sqlite3
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(get_database_path())
        cursor = conn.cursor()
        
        # استعلام للحصول على بيانات المؤسسة
        cursor.execute("SELECT المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        
        if result:
            return {
                "school_name": result[0],
                "academic_year": result[1]
            }
        else:
            return {
                "school_name": "الثانوية التأهيلية",
                "academic_year": "2024/2025"
            }
    except Exception as e:
        print(f"خطأ في الحصول على بيانات المؤسسة: {e}")
        return {
            "school_name": "الثانوية التأهيلية",
            "academic_year": "2024/2025"
        }
    finally:
        if 'conn' in locals():
            conn.close()

# تعديل الدالة الرئيسية لاختبار نموذج التأخر
def test_print():
    """اختبار الطباعة"""
    # عرض معلومات قاعدة البيانات للتشخيص
    print_db_info()

    students = [
        {"rt": "1", "name": "محمد أحمد"},
        {"rt": "2", "name": "سعيد علي"}
    ]
    section = "3-2"

    print("=== بدء اختبار طباعة ورقة الدخول (حرارية محسنة) ===")
    result1 = print_entry_form(students, section)
    print(f"نتيجة طباعة ورقة الدخول: {'نجاح' if result1 else 'فشل'}")

    print("\n=== بدء اختبار طباعة ورقة زيارة الطبيب ===")
    result2 = print_doctor_visit_form(students, section)
    print(f"نتيجة طباعة ورقة زيارة الطبيب: {'نجاح' if result2 else 'فشل'}")

def test_thermal_print_only():
    """اختبار الطباعة الحرارية فقط بدون قاعدة البيانات"""
    students = [
        {"rt": "1", "name": "أحمد محمد العلوي"},
        {"rt": "2", "name": "فاطمة سعيد البناني"},
        {"rt": "3", "name": "عبد الرحمن خالد التازي"}
    ]
    section = "3-2"
    
    print("=== اختبار الطباعة الحرارية المباشرة ===")
    
    # محتوى تجريبي مع جدول محسن وخطوط زخرفة
    content = """============================
الثانوية التأهيلية ابن رشد
============================
السنة الدراسية: 2024/2025
ورقة الدخول
التلاميذ المؤهلون للدخول إلى المؤسسة
في الحصة المحددة بتوقيت المناوبة
رت	           الاسم الكامل	
1	           أحمد محمد العلوي
2	           فاطمة سعيد البناني
3	           عبد الرحمن خالد التازي
من قســـــم : 3-2
التاريخ: 2025/09/03  الوقت: 14:30



"""
    
    result = thermal_print(content)
    print(f"نتيجة الطباعة الحرارية: {'نجاح' if result else 'فشل'}")

if __name__ == "__main__":
    # إنشاء تطبيق Qt قبل استخدام QPrinter
    from PyQt5.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    
    test_print()
    print("\n" + "="*50)
    test_thermal_print_only()
    
    # إنهاء التطبيق
    app.quit()
