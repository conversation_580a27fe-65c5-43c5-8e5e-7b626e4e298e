#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QPushButton, QMessageBox, QDialog)
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtGui import QFont, QIcon
from PyQt5.QtCore import Qt, QUrl, pyqtSignal, QDate, QObject, pyqtSlot
from PyQt5.QtSql import QSqlQuery
import sqlite3
import json
from urllib.parse import unquote
from datetime import datetime
from database_config import get_database_path, get_database_connection

# استيراد دالة الطباعة المتطورة (سيتم إضافتها لاحقاً)
try:
    from arabic_pdf_report import print_regulations_card as arabic_print_function
    ARABIC_PDF_AVAILABLE = True
    print("تم استيراد دالة الطباعة من arabic_pdf_report.py بنجاح")
except ImportError:
    ARABIC_PDF_AVAILABLE = False
    print("تعذر استيراد arabic_pdf_report.py")

class WebBridge(QObject):
    """فئة الجسر بين JavaScript و Python"""
    
    # إشارات للتفاعل مع النافذة الرئيسية
    dataRequested = pyqtSignal()
    sectionChanged = pyqtSignal(str)
    printCard = pyqtSignal()
    showStudentsByAge = pyqtSignal(int)
    showStudentsByAbsences = pyqtSignal(int)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        
    @pyqtSlot(str)
    def changeSection(self, section):
        """تغيير القسم المحدد"""
        try:
            self.sectionChanged.emit(section)
        except Exception as e:
            print(f"خطأ في تغيير القسم: {e}")
            self.showMessage("حدث خطأ في تغيير القسم", "error")
    
    @pyqtSlot()
    def requestPrint(self):
        """طلب طباعة بطاقة اللوائح"""
        try:
            self.printCard.emit()
        except Exception as e:
            print(f"خطأ في طلب الطباعة: {e}")
            self.showMessage("حدث خطأ أثناء محاولة الطباعة", "error")
    
    @pyqtSlot(int)
    def showStudentsAge(self, age):
        """عرض قائمة التلاميذ بالسن المحدد"""
        try:
            self.showStudentsByAge.emit(age)
        except Exception as e:
            print(f"خطأ في عرض التلاميذ بالسن: {e}")
    
    @pyqtSlot(int)
    def showStudentsAbsences(self, range_index):
        """عرض قائمة التلاميذ بمجموع الغياب المحدد"""
        try:
            self.showStudentsByAbsences.emit(range_index)
        except Exception as e:
            print(f"خطأ في عرض التلاميذ بالغياب: {e}")
    
    @pyqtSlot()
    def refreshData(self):
        """طلب تحديث البيانات"""
        try:
            self.dataRequested.emit()
        except Exception as e:
            print(f"خطأ في تحديث البيانات: {e}")
            self.showMessage("حدث خطأ أثناء تحديث البيانات", "error")
    
    @pyqtSlot(str)
    def handleConfirmationResult(self, result_data):
        """معالجة نتيجة التأكيد من JavaScript"""
        try:
            result = json.loads(result_data)
            action = result.get('action')
            confirmed = result.get('confirmed', False)
            data = result.get('data')
            
            if action == "confirm_print" and confirmed:
                # معالجة تأكيد الطباعة
                self.parent_window.process_print_confirmation()
            elif action == "open_file" and confirmed:
                # فتح الملف
                filepath = result.get('filepath')
                if filepath:
                    try:
                        # معالجة المسار لتجنب مشاكل الفواصل العكسية
                        filepath = filepath.replace('\\', '\\\\')
                        os.startfile(filepath)
                    except Exception as e:
                        self.showMessage(f"حدث خطأ أثناء فتح الملف: {str(e)}", "error")
        except Exception as e:
            print(f"خطأ في معالجة نتيجة التأكيد: {e}")
    
    def showMessage(self, message, message_type="info"):
        """عرض رسالة للمستخدم عبر JavaScript فقط"""
        try:
            # إرسال الرسالة لـ JavaScript للعرض في الواجهة
            message_data = json.dumps({"message": message, "type": message_type}, ensure_ascii=False)
            if self.parent_window and hasattr(self.parent_window, 'web_view'):
                self.parent_window.web_view.page().runJavaScript(f"showNotification({message_data});")
        except Exception as e:
            print(f"خطأ في عرض الرسالة: {e}")

class RegulationsCardHtmlWindow(QMainWindow):
    """نافذة بطاقة اللوائح والإحصائيات باستخدام HTML"""
    
    def __init__(self, parent=None, db=None, section=None, academic_year=None):
        super().__init__(parent)
        self.db = db
        self.section = section or "TCS-1"
        self.academic_year = academic_year or "2024/2025"
        self.db_path = get_database_path()
        self.using_qsql = bool(db and hasattr(db, 'isOpen') and db.isOpen())
        
        # متغيرات لتخزين البيانات
        self.students_data = []
        self.age_groups = {}
        self.absence_groups = {}
        self.stats_data = {}
        
        self.setWindowTitle(f"بطاقة اللوائح - {self.section}")
        self.setMinimumSize(1200, 800)
        
        # إعداد أيقونة النافذة
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "01.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except:
            pass
        
        self.setup_ui()
        self.setup_bridge()
        self.load_html_content()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء عارض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)
        
    def setup_bridge(self):
        """إعداد الجسر بين Python و JavaScript"""
        self.bridge = WebBridge(self)
        
        # ربط الإشارات
        self.bridge.dataRequested.connect(self.load_data)
        self.bridge.sectionChanged.connect(self.change_section)
        self.bridge.printCard.connect(self.print_regulations_card)
        self.bridge.showStudentsByAge.connect(self.show_students_by_age)
        self.bridge.showStudentsByAbsences.connect(self.show_students_by_absences)
        
    def load_html_content(self):
        """تحميل محتوى HTML"""
        html_content = self.generate_html()
        self.web_view.setHtml(html_content)
        
        # تحميل البيانات بعد تحميل HTML
        self.web_view.loadFinished.connect(self.on_load_finished)
        
    def on_load_finished(self):
        """يتم استدعاؤها عند انتهاء تحميل الصفحة"""
        # إعداد القناة للتواصل مع JavaScript
        from PyQt5.QtWebChannel import QWebChannel
        
        self.channel = QWebChannel()
        self.channel.registerObject("bridge", self.bridge)
        self.web_view.page().setWebChannel(self.channel)
        
        # تأخير قصير للتأكد من تحميل QWebChannel
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(500, self.load_data)
    
    def load_sections(self):
        """تحميل قائمة الأقسام من قاعدة البيانات"""
        sections = []
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود جدول_عام
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_عام'")
            has_regulations_table = cursor.fetchone() is not None
            
            if has_regulations_table:
                # التحقق من الأعمدة المتاحة
                cursor.execute("PRAGMA table_info(جدول_عام)")
                columns_info = cursor.fetchall()
                available_columns = [col[1] for col in columns_info]
                
                # البحث عن عمود القسم
                section_column = None
                for col_name in ['القسم', 'الفصل', 'الصف']:
                    if col_name in available_columns:
                        section_column = col_name
                        break
                
                # البحث عن عمود السنة الدراسية
                year_column = None
                for col_name in ['السنة_الدراسية', 'السنة', 'العام_الدراسي']:
                    if col_name in available_columns:
                        year_column = col_name
                        break
                
                if section_column:
                    if year_column:
                        # الحصول على قائمة الأقسام الفريدة للسنة المحددة
                        cursor.execute(f"""
                            SELECT DISTINCT {section_column}
                            FROM جدول_عام
                            WHERE {year_column} = ?
                            ORDER BY {section_column}
                        """, (self.academic_year,))
                    else:
                        # الحصول على قائمة الأقسام الفريدة بدون تصفية السنة
                        cursor.execute(f"""
                            SELECT DISTINCT {section_column}
                            FROM جدول_عام
                            ORDER BY {section_column}
                        """)
                    
                    sections = [row[0] for row in cursor.fetchall() if row[0]]
                    print(f"الأقسام المتاحة: {sections}")
                else:
                    print("لم يتم العثور على عمود القسم في قاعدة البيانات")
            else:
                print("جدول 'جدول_عام' غير موجود")
            
            conn.close()
            
            # إضافة قسم افتراضي إذا لم يتم العثور على أقسام
            if not sections:
                sections = ["TCS-1", "TCS-2", "TCS-3", "1BACSE-1", "1BACSE-2", "1BACSH-1"]
                print("تم إضافة أقسام افتراضية")
                
        except Exception as e:
            print(f"خطأ في تحميل قائمة الأقسام: {str(e)}")
            import traceback
            traceback.print_exc()
            # إضافة أقسام افتراضية في حالة الخطأ
            sections = ["TCS-1", "TCS-2", "TCS-3", "1BACSE-1", "1BACSE-2", "1BACSH-1"]
        
        return sections
    
    def get_students_data(self):
        """الحصول على بيانات التلاميذ من قاعدة البيانات"""
        students = []
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود جدول_عام
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_عام'")
            has_regulations_table = cursor.fetchone() is not None
            
            if not has_regulations_table:
                print("جدول 'جدول_عام' غير موجود في قاعدة البيانات")
                conn.close()
                return students
            
            # التحقق من الأعمدة المتاحة في الجدول
            cursor.execute("PRAGMA table_info(جدول_عام)")
            columns_info = cursor.fetchall()
            available_columns = [col[1] for col in columns_info]
            print(f"الأعمدة المتاحة في جدول جدول_عام: {available_columns}")
            
            # بناء الاستعلام حسب الأعمدة المتاحة
            base_columns = ['الرمز', 'الرقم_الترتيبي', 'الاسم_واللقب', 'الجنس', 'تاريخ_الميلاد']
            optional_columns = ['مجموع_الغياب', 'مجموع_التأخر', 'مجموع_السماح']
            
            # التحقق من أسماء الأعمدة البديلة
            column_mapping = {
                'الرقم_الترتيبي': ['رت', 'الرقم_الترتيبي', 'الترتيب'],
                'الاسم_واللقب': ['الاسم_والنسب', 'الاسم_واللقب', 'الاسم'],
                'الجنس': ['النوع', 'الجنس'],
                'تاريخ_الميلاد': ['تاريخ_الازدياد', 'تاريخ_الميلاد'],
                'مجموع_الغياب': ['مجموع_الغياب', 'الغياب', 'عدد_الغياب'],
                'مجموع_التأخر': ['مجموع_التأخر', 'التأخر', 'عدد_التأخر'],
                'مجموع_السماح': ['مجموع_السماح', 'السماح', 'عدد_السماح']
            }
            
            # تحديد الأعمدة الصحيحة
            final_columns = []
            for col in base_columns + optional_columns:
                found_col = None
                if col in available_columns:
                    found_col = col
                elif col in column_mapping:
                    for alt_col in column_mapping[col]:
                        if alt_col in available_columns:
                            found_col = alt_col
                            break
                
                if found_col:
                    final_columns.append(found_col)
                else:
                    final_columns.append("'' as " + col)
            
            # الحصول على بيانات التلاميذ في القسم المحدد
            query = f"""
                SELECT {', '.join(final_columns[:8])}
                FROM جدول_عام
                WHERE القسم = ? AND السنة_الدراسية = ?
                ORDER BY رت
            """
            
            print(f"الاستعلام: {query}")
            print(f"المعايير: القسم={self.section}, السنة_الدراسية={self.academic_year}")
            
            cursor.execute(query, (self.section, self.academic_year))
            rows = cursor.fetchall()
            
            print(f"تم العثور على {len(rows)} صف من البيانات")
            
            for i, row in enumerate(rows):
                print(f"الصف {i+1}: {row}")
                
                # حساب السن
                age = 0
                birth_date_str = row[4] if len(row) > 4 else None
                if birth_date_str:
                    try:
                        # تجربة تنسيقات مختلفة للتاريخ
                        for date_format in ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]:
                            try:
                                birth_date = datetime.strptime(str(birth_date_str), date_format)
                                today = datetime.now()
                                age = today.year - birth_date.year
                                if today.month < birth_date.month or (today.month == birth_date.month and today.day < birth_date.day):
                                    age -= 1
                                break
                            except ValueError:
                                continue
                    except Exception as e:
                        print(f"خطأ في تحليل تاريخ الميلاد {birth_date_str}: {e}")
                        age = 0
                
                student = {
                    'code': str(row[0]) if row[0] else '',
                    'rt': str(row[1]) if len(row) > 1 and row[1] else '',
                    'name': str(row[2]) if len(row) > 2 and row[2] else '',
                    'gender': str(row[3]) if len(row) > 3 and row[3] else '',
                    'birth_date': str(birth_date_str) if birth_date_str else '',
                    'age': age,
                    'absences': int(row[5]) if len(row) > 5 and row[5] and str(row[5]).isdigit() else 0,
                    'lates': int(row[6]) if len(row) > 6 and row[6] and str(row[6]).isdigit() else 0,
                    'permissions': int(row[7]) if len(row) > 7 and row[7] and str(row[7]).isdigit() else 0
                }
                students.append(student)
                print(f"تم إضافة التلميذ: {student}")
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في الحصول على بيانات التلاميذ: {str(e)}")
            import traceback
            traceback.print_exc()
        
        return students
    
    def calculate_statistics(self, students):
        """حساب الإحصائيات والتحليلات"""
        stats = {
            'total_students': len(students),
            'male_count': sum(1 for s in students if s.get('gender') in ['ذكر', 'ذ']),
            'female_count': 0,
            'total_absences': sum(s.get('absences', 0) for s in students),
            'total_lates': sum(s.get('lates', 0) for s in students),
            'total_permissions': sum(s.get('permissions', 0) for s in students),
            'absence_rate': 0,
            'late_rate': 0,
            'permission_rate': 0,
            'regular_attendance': 0,
            'frequent_absence': 0,
            'frequent_late': 0,
            'frequent_permission': 0,
            'violations': 0,
            'last_update': QDate.currentDate().toString("yyyy-MM-dd")
        }
        
        stats['female_count'] = stats['total_students'] - stats['male_count']
        
        # حساب المعدلات
        if stats['total_students'] > 0:
            stats['absence_rate'] = stats['total_absences'] / stats['total_students']
            stats['late_rate'] = stats['total_lates'] / stats['total_students']
            stats['permission_rate'] = stats['total_permissions'] / stats['total_students']
        
        # حساب إحصائيات التفصيل
        for student in students:
            absences = student.get('absences', 0)
            lates = student.get('lates', 0)
            permissions = student.get('permissions', 0)
            
            # الحضور المنتظم
            if absences == 0 and lates == 0:
                stats['regular_attendance'] += 1
            
            # الغياب المتكرر
            if absences > 0:
                stats['frequent_absence'] += 1
            
            # التأخر المتكرر
            if lates > 0:
                stats['frequent_late'] += 1
            
            # السماح المتكرر
            if permissions > 0:
                stats['frequent_permission'] += 1
            
            # المخالفات (افتراضياً = 0 لأن الجدول لا يحتوي على حقل المخالفات)
            # يمكن إضافة هذا لاحقاً
        
        return stats
    
    def group_students_by_age(self, students):
        """تجميع التلاميذ حسب السن"""
        age_groups = {}
        for student in students:
            age = student.get('age', 0)
            if age not in age_groups:
                age_groups[age] = []
            age_groups[age].append(student)
        
        return age_groups
    
    def group_students_by_absences(self, students):
        """تجميع التلاميذ حسب فئات الغياب"""
        absence_ranges = [
            {"min": 0, "max": 9, "label": "أقل من 10"},
            {"min": 10, "max": 20, "label": "من 10 إلى 20"},
            {"min": 21, "max": 30, "label": "من 21 إلى 30"},
            {"min": 31, "max": float('inf'), "label": "أكثر من 30"}
        ]
        
        students_by_absences = {i: {"label": range_info["label"], "students": []} 
                               for i, range_info in enumerate(absence_ranges)}
        
        for student in students:
            absences = student.get('absences', 0)
            for i, range_info in enumerate(absence_ranges):
                if range_info["min"] <= absences <= range_info["max"]:
                    students_by_absences[i]["students"].append(student)
                    break
        
        return students_by_absences
    
    def load_data(self):
        """تحميل البيانات وإرسالها لـ JavaScript"""
        try:
            print(f"بدء تحميل البيانات للقسم: {self.section}")
            print(f"مسار قاعدة البيانات: {self.db_path}")
            print(f"السنة الدراسية: {self.academic_year}")
            
            # التحقق من وجود ملف قاعدة البيانات
            if not os.path.exists(self.db_path):
                print("ملف قاعدة البيانات غير موجود!")
                self.bridge.showMessage("ملف قاعدة البيانات غير موجود", "error")
                return
            
            # تحميل بيانات التلاميذ
            self.students_data = self.get_students_data()
            print(f"تم العثور على {len(self.students_data)} تلميذ")
            
            # حساب الإحصائيات
            self.stats_data = self.calculate_statistics(self.students_data)
            print(f"تم حساب الإحصائيات: {self.stats_data}")
            
            # تجميع التلاميذ
            self.age_groups = self.group_students_by_age(self.students_data)
            self.absence_groups = self.group_students_by_absences(self.students_data)
            
            # تحميل قائمة الأقسام
            sections = self.load_sections()
            print(f"الأقسام المتاحة: {sections}")
            
            # إعداد البيانات لإرسالها لـ JavaScript
            data = {
                'sections': sections,
                'current_section': self.section,
                'academic_year': self.academic_year,
                'stats': self.stats_data,
                'age_groups': {str(age): len(students) for age, students in self.age_groups.items()},
                'absence_groups': {str(i): {"label": group["label"], "count": len(group["students"])} 
                                  for i, group in self.absence_groups.items()},
                'total_students': len(self.students_data)
            }
            
            print(f"البيانات المُرسلة لـ JavaScript: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            # إرسال البيانات لـ JavaScript
            js_data = json.dumps(data, ensure_ascii=False)
            self.web_view.page().runJavaScript(f"updateData({js_data});")
            
            print(f"تم تحميل بيانات القسم {self.section} بنجاح.")
            
        except Exception as e:
            print(f"خطأ أثناء تحميل البيانات: {str(e)}")
            import traceback
            traceback.print_exc()
            self.bridge.showMessage(f"حدث خطأ أثناء تحميل البيانات: {str(e)}", "error")
    
    def change_section(self, section):
        """تغيير القسم المحدد"""
        try:
            self.section = section
            self.setWindowTitle(f"بطاقة اللوائح - {section}")
            self.load_data()
        except Exception as e:
            print(f"خطأ في تغيير القسم: {str(e)}")
            self.bridge.showMessage("حدث خطأ أثناء تغيير القسم", "error")
    
    def show_students_by_age(self, age):
        """عرض قائمة التلاميذ بالسن المحدد"""
        try:
            if age in self.age_groups:
                students_list = self.age_groups[age]
                students_data = []
                
                for student in students_list:
                    students_data.append({
                        'code': student.get('code', ''),
                        'rt': student.get('rt', ''),
                        'name': student.get('name', ''),
                        'age': student.get('age', age)
                    })
                
                # إرسال قائمة التلاميذ لـ JavaScript
                js_data = json.dumps({
                    'title': f'قائمة التلاميذ ذوي السن {age} سنة',
                    'subtitle': f'عدد التلاميذ: {len(students_list)}',
                    'students': students_data,
                    'columns': ['الرمز', 'الرقم الترتيبي', 'الاسم والنسب', 'السن']
                }, ensure_ascii=False)
                
                self.web_view.page().runJavaScript(f"showStudentsDialog({js_data});")
                
        except Exception as e:
            print(f"خطأ في عرض التلاميذ بالسن: {str(e)}")
    
    def show_students_by_absences(self, range_index):
        """عرض قائمة التلاميذ بمجموع الغياب المحدد"""
        try:
            if range_index in self.absence_groups:
                group = self.absence_groups[range_index]
                students_list = group["students"]
                students_data = []
                
                for student in students_list:
                    students_data.append({
                        'code': student.get('code', ''),
                        'rt': student.get('rt', ''),
                        'name': student.get('name', ''),
                        'absences': student.get('absences', 0)
                    })
                
                # إرسال قائمة التلاميذ لـ JavaScript
                js_data = json.dumps({
                    'title': f'قائمة التلاميذ ذوي مجموع الغياب {group["label"]}',
                    'subtitle': f'عدد التلاميذ: {len(students_list)}',
                    'students': students_data,
                    'columns': ['الرمز', 'الرقم الترتيبي', 'الاسم والنسب', 'مجموع الغياب']
                }, ensure_ascii=False)
                
                self.web_view.page().runJavaScript(f"showStudentsDialog({js_data});")
                
        except Exception as e:
            print(f"خطأ في عرض التلاميذ بالغياب: {str(e)}")
    
    def print_regulations_card(self):
        """طباعة بطاقة اللوائح"""
        try:
            if ARABIC_PDF_AVAILABLE:
                # استخدام دالة الطباعة المتطورة
                filename = f"بطاقة_اللوائح_{self.section}_{self.academic_year}.pdf"
                
                # إعداد بيانات الطباعة
                print_data = {
                    'section': self.section,
                    'academic_year': self.academic_year,
                    'stats': self.stats_data,
                    'students': self.students_data,
                    'age_groups': self.age_groups,
                    'absence_groups': self.absence_groups
                }
                
                # استدعاء دالة الطباعة
                result = arabic_print_function(print_data, filename)
                
                if result.get('success'):
                    filepath = result.get('filepath')
                    # عرض رسالة تأكيد مع خيار فتح الملف
                    confirm_data = json.dumps({
                        'action': 'open_file',
                        'message': 'تم إنشاء ملف PDF بنجاح. هل تريد فتحه؟',
                        'filepath': filepath
                    }, ensure_ascii=False)
                    
                    self.web_view.page().runJavaScript(f"confirmAction({confirm_data});")
                else:
                    self.bridge.showMessage("حدث خطأ أثناء إنشاء ملف PDF", "error")
            else:
                # إشعار بعدم توفر دالة الطباعة
                self.bridge.showMessage("دالة الطباعة غير متوفرة حالياً", "warning")
                
        except Exception as e:
            print(f"خطأ في طباعة البطاقة: {str(e)}")
            self.bridge.showMessage("حدث خطأ أثناء الطباعة", "error")
    
    def process_print_confirmation(self):
        """معالجة تأكيد الطباعة"""
        try:
            self.print_regulations_card()
        except Exception as e:
            print(f"خطأ في معالجة تأكيد الطباعة: {str(e)}")
    
    def generate_html(self):
        """توليد محتوى HTML للواجهة"""
        return f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بطاقة اللوائح والإحصائيات</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }}
        
        body {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
            min-height: 180px;
            max-height: 180px;
            height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }}
        
        .header::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }}
        
        .header-content {{
            position: relative;
            z-index: 1;
        }}
        
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .header p {{
            font-size: 1.2em;
            opacity: 0.9;
        }}
        
        .controls {{
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }}
        
        .section-selector {{
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        
        .section-selector label {{
            font-weight: bold;
            color: #495057;
        }}
        
        .section-selector select {{
            padding: 8px 15px;
            border: 2px solid #ced4da;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            min-width: 200px;
            transition: all 0.3s ease;
        }}
        
        .section-selector select:focus {{
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.25);
        }}
        
        .action-buttons {{
            display: flex;
            gap: 10px;
        }}
        
        .btn {{
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }}
        
        .btn-primary {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }}
        
        .btn-primary:hover {{
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }}
        
        .btn-success {{
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }}
        
        .btn-success:hover {{
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }}
        
        .content {{
            padding: 30px;
        }}
        
        .tabs {{
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 30px;
            overflow-x: auto;
        }}
        
        .tab {{
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            font-weight: bold;
            color: #6c757d;
            transition: all 0.3s ease;
            white-space: nowrap;
            position: relative;
        }}
        
        .tab.active {{
            color: #667eea;
        }}
        
        .tab.active::after {{
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 3px 3px 0 0;
        }}
        
        .tab:hover {{
            color: #495057;
            background: rgba(102, 126, 234, 0.1);
        }}
        
        .tab-content {{
            display: none;
            animation: fadeIn 0.5s ease;
        }}
        
        .tab-content.active {{
            display: block;
        }}
        
        @keyframes fadeIn {{
            from {{ opacity: 0; transform: translateY(20px); }}
            to {{ opacity: 1; transform: translateY(0); }}
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        
        .stat-card {{
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }}
        
        .stat-card h3 {{
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.1em;
        }}
        
        .stat-value {{
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }}
        
        .stat-label {{
            color: #6c757d;
            font-size: 0.9em;
        }}
        
        .table-container {{
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }}
        
        .table {{
            width: 100%;
            border-collapse: collapse;
        }}
        
        .table th {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }}
        
        .table td {{
            padding: 12px 15px;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
            transition: background-color 0.3s ease;
        }}
        
        .table tbody tr:hover {{
            background-color: #f8f9fa;
            cursor: pointer;
        }}
        
        .table tbody tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        
        .table tbody tr:nth-child(even):hover {{
            background-color: #e9ecef;
        }}
        
        .clickable-row {{
            cursor: pointer;
            transition: all 0.3s ease;
        }}
        
        .clickable-row:hover {{
            background-color: #e3f2fd !important;
            transform: scale(1.02);
        }}
        
        /* نمط النوافذ المنبثقة */
        .modal {{
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }}
        
        .modal-content {{
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: modalShow 0.3s ease;
        }}
        
        @keyframes modalShow {{
            from {{ opacity: 0; transform: scale(0.7); }}
            to {{ opacity: 1; transform: scale(1); }}
        }}
        
        .modal-header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        
        .modal-header h2 {{
            margin: 0;
            font-size: 1.5em;
        }}
        
        .close {{
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }}
        
        .close:hover {{
            transform: scale(1.2);
        }}
        
        .modal-body {{
            padding: 20px;
        }}
        
        /* نمط الإشعارات */
        .notification {{
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            max-width: 400px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            animation: slideIn 0.3s ease;
        }}
        
        @keyframes slideIn {{
            from {{ transform: translateX(100%); opacity: 0; }}
            to {{ transform: translateX(0); opacity: 1; }}
        }}
        
        .notification.success {{ background-color: #28a745; }}
        .notification.error {{ background-color: #dc3545; }}
        .notification.warning {{ background-color: #ffc107; color: #212529; }}
        .notification.info {{ background-color: #17a2b8; }}
        
        /* تحسينات الاستجابة */
        @media (max-width: 768px) {{
            .header {{
                min-height: 160px;
                max-height: 160px;
                height: 160px;
                padding: 20px;
            }}
            
            .container {{
                margin: 10px;
                border-radius: 10px;
            }}
            
            .header h1 {{
                font-size: 2em;
            }}
            
            .controls {{
                flex-direction: column;
                align-items: stretch;
            }}
            
            .action-buttons {{
                justify-content: center;
            }}
            
            .stats-grid {{
                grid-template-columns: 1fr;
            }}
            
            .modal-content {{
                width: 95%;
                margin: 10% auto;
            }}
        }}
        
        .loading {{
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }}
        
        .loading::after {{
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }}
        
        @keyframes spin {{
            0% {{ transform: rotate(0deg); }}
            100% {{ transform: rotate(360deg); }}
        }}
        
        .empty-state {{
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }}
        
        .empty-state i {{
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1>بطاقة اللوائح والإحصائيات</h1>
                <p id="academicYear">السنة الدراسية: {self.academic_year}</p>
            </div>
        </div>
        
        <div class="controls">
            <div class="section-selector">
                <label for="sectionSelect">اختيار القسم:</label>
                <select id="sectionSelect" onchange="changeSection()">
                    <option value="">جاري التحميل...</option>
                </select>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-success" onclick="refreshData()">
                    🔄 تحديث البيانات
                </button>
                <button class="btn btn-primary" onclick="printCard()">
                    🖨️ طباعة البطاقة
                </button>
            </div>
        </div>
        
        <div class="content">
            <div class="tabs">
                <button class="tab active" onclick="showTab('general')">المعلومات العامة</button>
                <button class="tab" onclick="showTab('age')">التلاميذ حسب السن</button>
                <button class="tab" onclick="showTab('stats')">إحصاءات التأخر والغياب</button>
                <button class="tab" onclick="showTab('absences')">التلاميذ حسب الغياب</button>
            </div>
            
            <!-- علامة تبويب المعلومات العامة -->
            <div id="general-tab" class="tab-content active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>العدد الإجمالي للتلاميذ</h3>
                        <div class="stat-value" id="totalStudents">-</div>
                        <div class="stat-label">تلميذ</div>
                    </div>
                    <div class="stat-card">
                        <h3>عدد الذكور</h3>
                        <div class="stat-value" id="maleCount">-</div>
                        <div class="stat-label">تلميذ</div>
                    </div>
                    <div class="stat-card">
                        <h3>عدد الإناث</h3>
                        <div class="stat-value" id="femaleCount">-</div>
                        <div class="stat-label">تلميذة</div>
                    </div>
                    <div class="stat-card">
                        <h3>معدل الغياب</h3>
                        <div class="stat-value" id="absenceRate">-</div>
                        <div class="stat-label">حصة/تلميذ</div>
                    </div>
                    <div class="stat-card">
                        <h3>معدل التأخر</h3>
                        <div class="stat-value" id="lateRate">-</div>
                        <div class="stat-label">حالة/تلميذ</div>
                    </div>
                    <div class="stat-card">
                        <h3>معدل السماح</h3>
                        <div class="stat-value" id="permissionRate">-</div>
                        <div class="stat-label">حالة/تلميذ</div>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>البيان</th>
                                <th>القيمة</th>
                            </tr>
                        </thead>
                        <tbody id="generalInfoTable">
                            <tr>
                                <td>تاريخ آخر تحديث</td>
                                <td id="lastUpdate">-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- علامة تبويب التلاميذ حسب السن -->
            <div id="age-tab" class="tab-content">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>السن</th>
                                <th>عدد التلاميذ</th>
                            </tr>
                        </thead>
                        <tbody id="ageTable">
                            <tr>
                                <td colspan="2" class="loading">جاري تحميل البيانات...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- علامة تبويب الإحصاءات -->
            <div id="stats-tab" class="tab-content">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>البيان</th>
                                <th>العدد</th>
                                <th>المجموع</th>
                                <th>النسبة المئوية</th>
                            </tr>
                        </thead>
                        <tbody id="statsTable">
                            <tr>
                                <td colspan="4" class="loading">جاري تحميل البيانات...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- علامة تبويب التلاميذ حسب الغياب -->
            <div id="absences-tab" class="tab-content">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>فئة الغياب</th>
                                <th>عدد التلاميذ</th>
                            </tr>
                        </thead>
                        <tbody id="absencesTable">
                            <tr>
                                <td colspan="2" class="loading">جاري تحميل البيانات...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- النافذة المنبثقة لعرض قوائم التلاميذ -->
    <div id="studentsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">قائمة التلاميذ</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p id="modalSubtitle"></p>
                <div class="table-container">
                    <table class="table">
                        <thead id="modalTableHeader">
                        </thead>
                        <tbody id="modalTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <script>
        let bridge = null;
        let currentData = null;
        
        // إعداد قناة التواصل مع Python
        window.addEventListener('load', function() {{
            if (typeof QWebChannel !== 'undefined' && typeof qt !== 'undefined') {{
                new QWebChannel(qt.webChannelTransport, function(channel) {{
                    bridge = channel.objects.bridge;
                    console.log("تم إنشاء الجسر بنجاح");
                }});
            }} else {{
                console.log("QWebChannel أو qt غير متوفر");
                // في حالة عدم توفر QWebChannel، استخدم كائن وهمي
                bridge = {{
                    changeSection: function(section) {{ console.log("تغيير القسم إلى:", section); }},
                    refreshData: function() {{ console.log("تحديث البيانات"); }},
                    requestPrint: function() {{ console.log("طلب طباعة"); }}
                }};
            }}
        }});
        
        // وظائف علامات التبويب
        function showTab(tabName) {{
            // إخفاء جميع علامات التبويب
            const tabs = document.querySelectorAll('.tab');
            const contents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => tab.classList.remove('active'));
            contents.forEach(content => content.classList.remove('active'));
            
            // إظهار علامة التبويب المحددة
            event.target.classList.add('active');
            document.getElementById(tabName + '-tab').classList.add('active');
        }}
        
        // تغيير القسم
        function changeSection() {{
            const select = document.getElementById('sectionSelect');
            const selectedSection = select.value;
            
            if (selectedSection && bridge && typeof bridge.changeSection === 'function') {{
                bridge.changeSection(selectedSection);
            }}
        }}
        
        // تحديث البيانات
        function refreshData() {{
            if (bridge && typeof bridge.refreshData === 'function') {{
                showNotification({{ message: "جاري تحديث البيانات...", type: "info" }});
                bridge.refreshData();
            }}
        }}
        
        // طباعة البطاقة
        function printCard() {{
            if (bridge && typeof bridge.requestPrint === 'function') {{
                confirmAction({{
                    action: 'confirm_print',
                    message: 'هل تريد طباعة بطاقة اللوائح للقسم المحدد؟',
                    confirmText: 'طباعة',
                    cancelText: 'إلغاء'
                }});
            }}
        }}
        
        // تحديث البيانات من Python
        function updateData(data) {{
            currentData = data;
            
            // تحديث قائمة الأقسام
            const sectionSelect = document.getElementById('sectionSelect');
            sectionSelect.innerHTML = '';
            
            data.sections.forEach(section => {{
                const option = document.createElement('option');
                option.value = section;
                option.textContent = section;
                if (section === data.current_section) {{
                    option.selected = true;
                }}
                sectionSelect.appendChild(option);
            }});
            
            // تحديث الإحصائيات العامة
            updateGeneralStats(data.stats);
            
            // تحديث جداول البيانات
            updateAgeTable(data.age_groups);
            updateStatsTable(data.stats);
            updateAbsencesTable(data.absence_groups);
            
            showNotification({{ message: "تم تحديث البيانات بنجاح", type: "success" }});
        }}
        
        // تحديث الإحصائيات العامة
        function updateGeneralStats(stats) {{
            document.getElementById('totalStudents').textContent = stats.total_students || 0;
            document.getElementById('maleCount').textContent = stats.male_count || 0;
            document.getElementById('femaleCount').textContent = stats.female_count || 0;
            document.getElementById('absenceRate').textContent = (stats.absence_rate || 0).toFixed(2);
            document.getElementById('lateRate').textContent = (stats.late_rate || 0).toFixed(2);
            document.getElementById('permissionRate').textContent = (stats.permission_rate || 0).toFixed(2);
            document.getElementById('lastUpdate').textContent = stats.last_update || 'غير محدد';
        }}
        
        // تحديث جدول التلاميذ حسب السن
        function updateAgeTable(ageGroups) {{
            const tbody = document.getElementById('ageTable');
            tbody.innerHTML = '';
            
            if (Object.keys(ageGroups).length === 0) {{
                tbody.innerHTML = '<tr><td colspan="2" class="empty-state">لا توجد بيانات</td></tr>';
                return;
            }}
            
            // ترتيب الأعمار
            const sortedAges = Object.keys(ageGroups).sort((a, b) => parseInt(a) - parseInt(b));
            
            sortedAges.forEach(age => {{
                const count = ageGroups[age];
                const row = document.createElement('tr');
                row.className = 'clickable-row';
                row.onclick = () => showStudentsByAge(parseInt(age));
                
                row.innerHTML = `
                    <td>${{age}} سنة</td>
                    <td>${{count}}</td>
                `;
                
                tbody.appendChild(row);
            }});
        }}
        
        // تحديث جدول الإحصاءات
        function updateStatsTable(stats) {{
            const tbody = document.getElementById('statsTable');
            tbody.innerHTML = '';
            
            const statsData = [
                {{
                    label: 'الحضور المنتظم',
                    count: stats.regular_attendance || 0,
                    total: '-',
                    percentage: stats.total_students > 0 ? ((stats.regular_attendance || 0) / stats.total_students * 100).toFixed(2) + '%' : '0.00%'
                }},
                {{
                    label: 'الغياب المتكرر',
                    count: stats.frequent_absence || 0,
                    total: stats.total_absences || 0,
                    percentage: stats.total_students > 0 ? ((stats.frequent_absence || 0) / stats.total_students * 100).toFixed(2) + '%' : '0.00%'
                }},
                {{
                    label: 'التأخر المتكرر',
                    count: stats.frequent_late || 0,
                    total: stats.total_lates || 0,
                    percentage: stats.total_students > 0 ? ((stats.frequent_late || 0) / stats.total_students * 100).toFixed(2) + '%' : '0.00%'
                }},
                {{
                    label: 'المخالفات',
                    count: stats.violations || 0,
                    total: stats.violations || 0,
                    percentage: stats.total_students > 0 ? ((stats.violations || 0) / stats.total_students * 100).toFixed(2) + '%' : '0.00%'
                }},
                {{
                    label: 'السماح المتكرر',
                    count: stats.frequent_permission || 0,
                    total: stats.total_permissions || 0,
                    percentage: stats.total_students > 0 ? ((stats.frequent_permission || 0) / stats.total_students * 100).toFixed(2) + '%' : '0.00%'
                }}
            ];
            
            statsData.forEach(item => {{
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${{item.label}}</td>
                    <td>${{item.count}}</td>
                    <td>${{item.total}}</td>
                    <td>${{item.percentage}}</td>
                `;
                tbody.appendChild(row);
            }});
        }}
        
        // تحديث جدول التلاميذ حسب الغياب
        function updateAbsencesTable(absenceGroups) {{
            const tbody = document.getElementById('absencesTable');
            tbody.innerHTML = '';
            
            if (Object.keys(absenceGroups).length === 0) {{
                tbody.innerHTML = '<tr><td colspan="2" class="empty-state">لا توجد بيانات</td></tr>';
                return;
            }}
            
            Object.keys(absenceGroups).forEach(index => {{
                const group = absenceGroups[index];
                const row = document.createElement('tr');
                row.className = 'clickable-row';
                row.onclick = () => showStudentsByAbsences(parseInt(index));
                
                row.innerHTML = `
                    <td>${{group.label}}</td>
                    <td>${{group.count}}</td>
                `;
                
                tbody.appendChild(row);
            }});
        }}
        
        // عرض التلاميذ بالسن المحدد
        function showStudentsByAge(age) {{
            if (bridge && typeof bridge.showStudentsAge === 'function') {{
                bridge.showStudentsAge(age);
            }}
        }}
        
        // عرض التلاميذ بمجموع الغياب المحدد
        function showStudentsByAbsences(rangeIndex) {{
            if (bridge && typeof bridge.showStudentsAbsences === 'function') {{
                bridge.showStudentsAbsences(rangeIndex);
            }}
        }}
        
        // عرض نافذة قائمة التلاميذ
        function showStudentsDialog(data) {{
            const modal = document.getElementById('studentsModal');
            const title = document.getElementById('modalTitle');
            const subtitle = document.getElementById('modalSubtitle');
            const header = document.getElementById('modalTableHeader');
            const body = document.getElementById('modalTableBody');
            
            title.textContent = data.title;
            subtitle.textContent = data.subtitle;
            
            // إنشاء رأس الجدول
            header.innerHTML = '';
            const headerRow = document.createElement('tr');
            data.columns.forEach(column => {{
                const th = document.createElement('th');
                th.textContent = column;
                headerRow.appendChild(th);
            }});
            header.appendChild(headerRow);
            
            // إنشاء محتوى الجدول
            body.innerHTML = '';
            if (data.students.length === 0) {{
                const row = document.createElement('tr');
                row.innerHTML = `<td colspan="${{data.columns.length}}" class="empty-state">لا توجد بيانات</td>`;
                body.appendChild(row);
            }} else {{
                data.students.forEach(student => {{
                    const row = document.createElement('tr');
                    
                    // إضافة البيانات حسب الأعمدة
                    data.columns.forEach(column => {{
                        const td = document.createElement('td');
                        switch(column) {{
                            case 'الرمز':
                                td.textContent = student.code || '';
                                break;
                            case 'الرقم الترتيبي':
                                td.textContent = student.rt || '';
                                break;
                            case 'الاسم والنسب':
                                td.textContent = student.name || '';
                                break;
                            case 'السن':
                                td.textContent = student.age || '';
                                break;
                            case 'مجموع الغياب':
                                td.textContent = student.absences || '';
                                break;
                            default:
                                td.textContent = '';
                        }}
                        row.appendChild(td);
                    }});
                    
                    body.appendChild(row);
                }});
            }}
            
            modal.style.display = 'block';
        }}
        
        // إغلاق النافذة المنبثقة
        function closeModal() {{
            document.getElementById('studentsModal').style.display = 'none';
        }}
        
        // إغلاق النافذة المنبثقة عند النقر خارجها
        window.onclick = function(event) {{
            const modal = document.getElementById('studentsModal');
            if (event.target === modal) {{
                modal.style.display = 'none';
            }}
        }}
        
        // عرض الإشعارات
        function showNotification(data) {{
            const notification = document.createElement('div');
            notification.className = `notification ${{data.type}}`;
            notification.textContent = data.message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {{
                notification.style.animation = 'slideIn 0.3s ease reverse';
                setTimeout(() => {{
                    document.body.removeChild(notification);
                }}, 300);
            }}, 3000);
        }}
        
        // عرض نافذة التأكيد
        function confirmAction(data) {{
            const result = confirm(data.message);
            
            if (bridge && typeof bridge.handleConfirmationResult === 'function') {{
                const resultData = {{
                    action: data.action,
                    confirmed: result,
                    data: data
                }};
                
                if (data.filepath) {{
                    resultData.filepath = data.filepath;
                }}
                
                bridge.handleConfirmationResult(JSON.stringify(resultData));
            }}
        }}
        
        // التهيئة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {{
            console.log("تم تحميل الصفحة");
        }});
    </script>
</body>
</html>
        """

# دالة لتشغيل النافذة
def show_regulations_card_window(parent=None, db=None, section=None, academic_year=None):
    """دالة لإظهار نافذة بطاقة اللوائح والإحصائيات"""
    window = RegulationsCardHtmlWindow(parent, db, section, academic_year)
    window.show()
    return window

# للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = RegulationsCardHtmlWindow()
    window.show()
    sys.exit(app.exec_())
