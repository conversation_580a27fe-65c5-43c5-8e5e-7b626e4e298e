#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sqlite3
import json
from datetime import datetime
from database_config import get_database_path, get_database_connection
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import QWebEngineView


class ParentVisitHTMLWindow(QMainWindow):
    """نافذة توثيق زيارة أولياء الأمور باستخدام منهجية Python + HTML الحديثة"""
    
    def __init__(self, db_path=None, parent=None):
        super().__init__(parent)
        self.db_path = db_path if db_path is not None else get_database_path()
        self.current_student = None
        
        # تهيئة القيم الأولية (سيتم تحديثها من قاعدة البيانات)
        self.school_year = ""
        self.semester = ""
        
        # إعداد النافذة
        self.setupUI()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
        
        # إنشاء HTML وعرضه
        self.generate_html()

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📋")
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين حجم النافذة
        screen = QApplication.desktop().screenGeometry()
        width = int(screen.width() * 0.9)
        height = int(screen.height() * 0.85)
        self.resize(width, height)
        
        # توسيط النافذة
        self.move(
            (screen.width() - width) // 2,
            (screen.height() - height) // 2
        )
        
        # إضافة أيقونة للنافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # منطقة عرض HTML
        self.web_view = QWebEngineView()
        self.web_view.setStyleSheet("""
            QWebEngineView {
                border: 2px solid #1976d2;
                border-radius: 10px;
                background: white;
            }
        """)
        main_layout.addWidget(self.web_view)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                border-top: 1px solid #ddd;
                font-size: 12px;
                color: #666;
                padding: 5px;
            }
        """)
        self.status_bar.showMessage("جاهز...")

    def load_initial_data(self):
        """تحميل البيانات الأولية من قاعدة البيانات"""
        try:
            print("🔄 بدء تحميل البيانات الأولية...")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # فحص وجود جدول بيانات_المؤسسة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
                result = cursor.fetchone()
                
                if result:
                    self.school_year = str(result[0]) if result[0] else ""
                    self.semester = str(result[1]) if result[1] else ""
                    print(f"✅ تم تحميل البيانات: السنة الدراسية {self.school_year} - الأسدس {self.semester}")
                else:
                    print("⚠️ لا توجد بيانات في جدول بيانات_المؤسسة")
                    self.school_year = ""
                    self.semester = ""
            else:
                print("⚠️ جدول بيانات_المؤسسة غير موجود")
                self.school_year = ""
                self.semester = ""
            
            conn.close()
            
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات الأولية: {e}")
            self.school_year = ""
            self.semester = ""

    def generate_html(self):
        """توليد HTML لعرض النموذج"""
        current_date = datetime.now().strftime('%Y-%m-%d')
        current_time = datetime.now().strftime('%H:%M')
        
        html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>توثيق زيارة أولياء الأمور</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
            direction: rtl;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
            min-height: 180px;
            max-height: 180px;
            height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }}
        
        .header h1 {{
            font-family: 'Calibri', sans-serif;
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .header p {{
            font-family: 'Calibri', sans-serif;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        
        .search-section {{
            background: #e8f4fd;
            padding: 20px;
            border-bottom: 3px solid #3498db;
        }}
        
        .search-container {{
            display: flex;
            align-items: center;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }}
        
        .search-container label {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1e3a8a;
        }}
        
        .search-input {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1f2937;
            padding: 12px 20px;
            border: 2px solid #3498db;
            border-radius: 10px;
            min-width: 200px;
            background: white;
            transition: all 0.3s ease;
        }}
        
        .search-input:focus {{
            border-color: #2980b9;
            outline: none;
            box-shadow: 0 0 15px rgba(52, 152, 219, 0.4);
        }}
        
        .search-btn {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            padding: 12px 24px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }}
        
        .search-btn:hover {{
            background: linear-gradient(45deg, #2980b9, #1f4e79);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }}
        
        .main-content {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }}
        
        .form-section {{
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }}
        
        .section-title {{
            font-family: 'Calibri', sans-serif;
            font-size: 18px;
            font-weight: bold;
            color: #1e3a8a;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }}
        
        .form-grid {{
            display: grid;
            gap: 15px;
            margin-bottom: 20px;
        }}
        
        .form-row {{
            display: grid;
            grid-template-columns: 120px 1fr;
            gap: 10px;
            align-items: center;
        }}
        
        label {{
            font-family: 'Calibri', sans-serif;
            font-size: 16px;
            font-weight: bold;
            color: #1e3a8a;
        }}
        
        input[type="text"], input[type="date"], input[type="time"], 
        textarea, select {{
            font-family: 'Calibri', sans-serif;
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            transition: all 0.3s ease;
            background: white;
        }}
        
        /* إخفاء علامة الإكس من مربع التاريخ */
        input[type="date"]::-webkit-clear-button {{
            display: none;
        }}
        
        input[type="date"]::-webkit-inner-spin-button {{
            display: none;
        }}
        
        input[type="date"]::-webkit-calendar-picker-indicator {{
            cursor: pointer;
        }}
        
        input[type="text"]:focus, input[type="date"]:focus, 
        input[type="time"]:focus, textarea:focus, select:focus {{
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }}
        
        input[readonly] {{
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }}
        
        textarea {{
            resize: vertical;
            min-height: 100px;
        }}
        
        .button-group {{
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            grid-column: 1 / -1;
        }}
        
        .btn {{
            font-family: 'Calibri', sans-serif;
            font-size: 16px;
            font-weight: bold;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }}
        
        .btn-success {{
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
        }}
        
        .btn-success:hover {{
            background: linear-gradient(45deg, #219a52, #27ae60);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }}
        
        .btn-secondary {{
            background: linear-gradient(45deg, #95a5a6, #bdc3c7);
            color: white;
        }}
        
        .btn-secondary:hover {{
            background: linear-gradient(45deg, #7f8c8d, #95a5a6);
            transform: translateY(-2px);
        }}
        
        .alert {{
            font-family: 'Calibri', sans-serif;
            font-size: 16px;
            font-weight: bold;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }}
        
        .alert-success {{
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }}
        
        .alert-danger {{
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }}
        
        .alert-warning {{
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }}
        
        .loading {{
            font-family: 'Calibri', sans-serif;
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            display: none;
            text-align: center;
            padding: 20px;
        }}
        
        .spinner {{
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }}
        
        @keyframes spin {{
            0% {{ transform: rotate(0deg); }}
            100% {{ transform: rotate(360deg); }}
        }}
        
        @media (max-width: 768px) {{
            .header {{
                min-height: 160px;
                max-height: 160px;
                height: 160px;
                padding: 20px;
            }}
            
            .main-content {{
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }}
            
            .form-row {{
                grid-template-columns: 1fr;
            }}
            
            .search-container {{
                flex-direction: column;
            }}
            
            .search-input {{
                min-width: 100%;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 توثيق زيارة أولياء الأمور</h1>
            <p>نموذج توثيق زيارات أولياء الأمور للمؤسسة</p>
        </div>
        
        <!-- قسم البحث عن التلميذ -->
        <div class="search-section">
            <div class="search-container">
                <label for="student_search">🔍 البحث برمز التلميذ:</label>
                <input type="text" id="student_search" class="search-input" 
                       placeholder="أدخل رمز التلميذ..." 
                       onkeypress="handleEnterKey(event)">
                <button type="button" class="search-btn" onclick="searchStudent()">
                    🔍 بحث
                </button>
            </div>
        </div>
        
        <div class="main-content">
            <!-- قسم معلومات التلميذ -->
            <div class="form-section">
                <h2 class="section-title">  معلومات التلميذ</h2>
                
                <div id="alerts"></div>
                
                <div class="form-grid">
                    <div class="form-row">
                        <label>المستوى:</label>
                        <input type="text" id="level" readonly placeholder="غير محدد">
                    </div>
                    
                    <div class="form-row">
                        <label>القسم:</label>
                        <input type="text" id="class_name" readonly placeholder="غير محدد">
                    </div>
                    
                    <div class="form-row">
                        <label>الرمز:</label>
                        <input type="text" id="student_code" readonly placeholder="غير محدد">
                    </div>
                    
                    <div class="form-row">
                        <label>رت:</label>
                        <input type="text" id="student_id" readonly placeholder="غير محدد">
                    </div>
                    
                    <div class="form-row">
                        <label>الاسم والنسب:</label>
                        <input type="text" id="student_name" readonly placeholder="غير محدد">
                    </div>
                </div>
            </div>
            
            <!-- قسم معلومات الزيارة -->
            <div class="form-section">
                <h2 class="section-title">   معلومات الزيارة </h2>
                
                <div class="form-grid">
                    <div class="form-row">
                        <label>اسم الولي:</label>
                        <input type="text" id="parent_name" placeholder="أدخل اسم الولي">
                    </div>
                    
                    <div class="form-row">
                        <label>رقم البطاقة:</label>
                        <input type="text" id="id_card" placeholder="أدخل رقم البطاقة">
                    </div>
                    
                    <div class="form-row">
                        <label>تاريخ الزيارة:</label>
                        <input type="date" id="visit_date" value="{current_date}">
                    </div>
                    
                    <div class="form-row">
                        <label>وقت الزيارة:</label>
                        <input type="time" id="visit_time" value="{current_time}">
                    </div>
                    
                    <div class="form-row">
                        <label>سبب الزيارة:</label>
                        <select id="reason_select" onchange="updateReasonContent()">
                            <option value="">اختر السبب...</option>
                            <option value="متابعة الوضع الدراسي">متابعة الوضع الدراسي</option>
                            <option value="مناقشة سلوك التلميذ">مناقشة سلوك التلميذ</option>
                            <option value="استلام النتائج">استلام النتائج</option>
                            <option value="المشاركة في مجلس المؤسسة">المشاركة في مجلس المؤسسة</option>
                            <option value="طلب شهادة مدرسية">طلب شهادة مدرسية</option>
                            <option value="التواصل مع الأطر التربوية">التواصل مع الأطر التربوية</option>
                            <option value="الإبلاغ عن ظروف خاصة">الإبلاغ عن ظروف خاصة</option>
                            <option value="مواكبة الأنشطة التربوية">مواكبة الأنشطة التربوية</option>
                            <option value="تقديم شكاية أو اقتراح">تقديم شكاية أو اقتراح</option>
                            <option value="أخرى">أخرى...</option>
                        </select>
                    </div>
                    
                    <div class="form-row" style="grid-column: 1 / -1;">
                        <label>مضمون الزيارة:</label>
                        <textarea id="visit_content" placeholder="تفاصيل مضمون الزيارة..."></textarea>
                    </div>
                    
                    <div class="form-row">
                        <label>السنة الدراسية:</label>
                        <input type="text" id="school_year" value="{self.school_year}" readonly>
                    </div>
                    
                    <div class="form-row">
                        <label>الأسدس:</label>
                        <input type="text" id="semester" value="{self.semester}" readonly>
                    </div>
                </div>
            </div>
            
            <!-- أزرار التحكم -->
            <div class="button-group">
                <button class="btn btn-secondary" onclick="clearFields()">
                    🗑️ مسح البيانات
                </button>
                <button class="btn btn-success" onclick="saveVisit()">
                    💾 حفظ الزيارة
                </button>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جاري حفظ البيانات...</p>
            </div>
        </div>
    </div>
    
    <script>
        // متغير عام لتخزين طلبات الحفظ والبحث
        window.pendingSaveData = null;
        window.pendingSearchData = null;
        
        // قاموس أسباب الزيارة مع المضامين المقترحة
        const reasonsMap = {{
            "متابعة الوضع الدراسي": "حضر ولي الأمر قصد متابعة الوضع الدراسي لابنه/ابنته والتعرف على مستواه التعليمي داخل الفصل.",
            "مناقشة سلوك التلميذ": "جاء ولي الأمر لمناقشة سلوكيات ابنه/ابنته داخل المؤسسة ومعالجة بعض التصرفات التربوية.",
            "استلام النتائج": "زار ولي الأمر المؤسسة لاستلام نتائج الفروض أو التقارير الفصلية الخاصة بابنه/ابنته.",
            "المشاركة في مجلس المؤسسة": "حضر ولي الأمر بناءً على دعوة المؤسسة للمشاركة في أشغال مجلس المؤسسة التربوي.",
            "طلب شهادة مدرسية": "تقدم ولي الأمر بطلب الحصول على شهادة مدرسية أو وثيقة إدارية تخص ابنه/ابنته.",
            "التواصل مع الأطر التربوية": "زار ولي الأمر المؤسسة للتواصل مع الأستاذ/الإدارة بخصوص بعض الملاحظات أو الاستفسارات.",
            "الإبلاغ عن ظروف خاصة": "أبلغ ولي الأمر المؤسسة بوضعية اجتماعية أو صحية تخص ابنه/ابنته قد تؤثر على تحصيله الدراسي.",
            "مواكبة الأنشطة التربوية": "جاء ولي الأمر لمتابعة أو دعم نشاط تربوي أو ثقافي تنخرط فيه المؤسسة.",
            "تقديم شكاية أو اقتراح": "زار ولي الأمر المؤسسة من أجل تقديم شكاية أو اقتراح يهم الشأن التربوي والتعليم."
        }};
        
        function handleEnterKey(event) {{
            if (event.key === 'Enter') {{
                searchStudent();
            }}
        }}
        
        function searchStudent() {{
            const studentCode = document.getElementById('student_search').value.trim();
            if (!studentCode) {{
                showAlert('يرجى إدخال رمز التلميذ', 'warning');
                return;
            }}
            
            showAlert('جاري البحث في قاعدة البيانات...', 'warning');
            
            // تعيين علامة طلب البحث للمعالجة
            window.pendingSearchData = studentCode;
        }}
        
        function updateReasonContent() {{
            const selectedReason = document.getElementById('reason_select').value;
            const contentField = document.getElementById('visit_content');
            
            if (selectedReason && reasonsMap[selectedReason]) {{
                // فقط قم بتحديث المضمون إذا كان الحقل فارغاً أو يحتوي على اقتراح سابق
                const currentContent = contentField.value.trim();
                const isPreviousSuggestion = Object.values(reasonsMap).includes(currentContent);
                
                if (!currentContent || isPreviousSuggestion) {{
                    contentField.value = reasonsMap[selectedReason];
                }}
            }}
        }}
        
        function showAlert(message, type) {{
            const alertsContainer = document.getElementById('alerts');
            const alert = document.createElement('div');
            alert.className = `alert alert-${{type}}`;
            alert.textContent = message;
            alert.style.display = 'block';
            
            alertsContainer.innerHTML = '';
            alertsContainer.appendChild(alert);
            
            setTimeout(() => {{
                alert.style.display = 'none';
            }}, 5000);
        }}
        
        function validateForm() {{
            const studentCode = document.getElementById('student_code').value.trim();
            const parentName = document.getElementById('parent_name').value.trim();
            
            if (!studentCode) {{
                showAlert('الرجاء البحث عن تلميذ أولاً', 'danger');
                return false;
            }}
            
            if (!parentName) {{
                showAlert('الرجاء إدخال اسم الولي', 'danger');
                return false;
            }}
            
            return true;
        }}
        
        function saveVisit() {{
            console.log('💾 بدء عملية حفظ الزيارة...');
            
            if (!validateForm()) {{
                return;
            }}
            
            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            
            // جمع البيانات
            const data = {{
                student_code: document.getElementById('student_code').value,
                student_name: document.getElementById('student_name').value,
                student_id: document.getElementById('student_id').value,
                level: document.getElementById('level').value,
                class_name: document.getElementById('class_name').value,
                parent_name: document.getElementById('parent_name').value,
                id_card: document.getElementById('id_card').value,
                visit_date: document.getElementById('visit_date').value,
                visit_time: document.getElementById('visit_time').value,
                reason: document.getElementById('reason_select').value,
                content: document.getElementById('visit_content').value,
                school_year: document.getElementById('school_year').value,
                semester: document.getElementById('semester').value
            }};
            
            console.log('📋 البيانات المجمعة:', data);
            
            // حفظ البيانات للمعالجة
            window.pendingSaveData = data;
            
            console.log('✅ تم تعيين البيانات للحفظ');
        }}
        
        function setStudentInfo(code, name, id, level, className) {{
            try {{
                console.log('🔄 بدء تعيين معلومات التلميذ:', {{code, name, id, level, className}});

                document.getElementById('student_code').value = code || '';
                document.getElementById('student_name').value = name || '';
                document.getElementById('student_id').value = id || '';
                document.getElementById('level').value = level || '';
                document.getElementById('class_name').value = className || '';
                
                // تعيين نفس القيمة في مربع البحث
                document.getElementById('student_search').value = code || '';

                console.log('✅ تم تعيين جميع معلومات التلميذ بنجاح');
                showAlert(`تم العثور على التلميذ: ${{name}}`, 'success');

            }} catch(error) {{
                console.error('❌ خطأ في تعيين معلومات التلميذ:', error);
                showAlert('حدث خطأ في تحديد معلومات التلميذ', 'danger');
            }}
        }}
        
        function clearStudentInfo() {{
            document.getElementById('student_code').value = '';
            document.getElementById('student_name').value = '';
            document.getElementById('student_id').value = '';
            document.getElementById('level').value = '';
            document.getElementById('class_name').value = '';
            document.getElementById('student_search').value = '';
        }}
        
        function clearFields() {{
            if (confirm('هل تريد مسح جميع البيانات؟')) {{
                // مسح بيانات التلميذ
                clearStudentInfo();
                
                // مسح بيانات الزيارة
                document.getElementById('parent_name').value = '';
                document.getElementById('id_card').value = '';
                document.getElementById('visit_date').value = new Date().toISOString().split('T')[0];
                document.getElementById('visit_time').value = new Date().toTimeString().slice(0,5);
                document.getElementById('reason_select').selectedIndex = 0;
                document.getElementById('visit_content').value = '';
                
                showAlert('تم مسح جميع البيانات بنجاح', 'success');
            }}
        }}
        
        // دالة تأكيد نجاح الحفظ
        function confirmSaveSuccess(visitId) {{
            const loading = document.getElementById('loading');
            loading.style.display = 'none';
            
            const studentName = document.getElementById('student_name').value;
            const parentName = document.getElementById('parent_name').value;
            const visitDate = document.getElementById('visit_date').value;
            
            const successMessage = `تم حفظ زيارة ولي الأمر بنجاح! ✅\\n\\n` +
                                 `📋 تفاصيل الزيارة:\\n` +
                                 `👤 التلميذ: ${{studentName}}\\n` +
                                 `👨‍👩‍👧 ولي الأمر: ${{parentName}}\\n` +
                                 `📅 تاريخ الزيارة: ${{visitDate}}\\n\\n` +
                                 `رقم الزيارة: ${{visitId}}`;
            
            showAlert(successMessage, 'success');
            
            // مسح حقول الزيارة مع الاحتفاظ بمعلومات التلميذ
            setTimeout(() => {{
                document.getElementById('parent_name').value = '';
                document.getElementById('id_card').value = '';
                document.getElementById('reason_select').selectedIndex = 0;
                document.getElementById('visit_content').value = '';
                document.getElementById('visit_date').value = new Date().toISOString().split('T')[0];
                document.getElementById('visit_time').value = new Date().toTimeString().slice(0,5);
                showAlert('تم مسح بيانات الزيارة وهو جاهز لإدخال زيارة جديدة 🔄', 'warning');
            }}, 3000);
        }}
        
        // دالة تأكيد فشل الحفظ
        function confirmSaveError(message) {{
            const loading = document.getElementById('loading');
            loading.style.display = 'none';
            showAlert(message || 'فشل في حفظ الزيارة ❌', 'danger');
        }}
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {{
            console.log('📄 تم تحميل صفحة توثيق زيارة أولياء الأمور');
        }});
    </script>
</body>
</html>
        """
        
        self.web_view.setHtml(html_content)
        
        # إضافة قناة للتواصل مع JavaScript
        self.setup_js_bridge()

    def setup_js_bridge(self):
        """إعداد قناة التواصل مع JavaScript"""
        try:
            print("🔧 إعداد قناة التواصل مع JavaScript...")

            # إعداد مؤقت للتحقق من طلبات البحث
            self.search_timer = QTimer()
            self.search_timer.timeout.connect(self.check_search_requests)
            self.search_timer.start(500)

            # إعداد مؤقت للتحقق من طلبات الحفظ
            self.save_timer = QTimer()
            self.save_timer.timeout.connect(self.check_save_requests)
            self.save_timer.start(300)

        except Exception as e:
            print(f"❌ خطأ في إعداد قناة JavaScript: {e}")

    def check_search_requests(self):
        """فحص طلبات البحث من JavaScript"""
        try:
            js_code = """
                (function() {
                    if (window.pendingSearchData) {
                        const searchCode = window.pendingSearchData;
                        window.pendingSearchData = null;
                        return searchCode;
                    }
                    return '';
                })();
            """
            
            def handle_search_result(result):
                if result and result.strip():
                    print(f"🔍 تم استلام طلب البحث: {result}")
                    self.search_student_by_code(result.strip())
            
            self.web_view.page().runJavaScript(js_code, handle_search_result)
            
        except Exception as e:
            print(f"❌ خطأ في فحص طلبات البحث: {e}")

    def check_save_requests(self):
        """فحص طلبات الحفظ من JavaScript"""
        try:
            js_code = """
                (function() {
                    if (window.pendingSaveData) {
                        const data = window.pendingSaveData;
                        window.pendingSaveData = null;
                        return JSON.stringify(data);
                    }
                    return '';
                })();
            """
            
            def handle_save_request(result):
                if result and result.strip():
                    print(f"📥 تم استلام طلب حفظ: {result}")
                    self.handle_save_request(result)
            
            self.web_view.page().runJavaScript(js_code, handle_save_request)
            
        except Exception as e:
            print(f"❌ خطأ في فحص طلبات الحفظ: {e}")

    def search_student_by_code(self, student_code):
        """البحث عن التلميذ برمزه"""
        try:
            print(f"🔍 البحث عن التلميذ برمزه: {student_code}")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الاستعلام المركزي
            central_query = """
            SELECT 
                s.الرمز,
                s.الاسم_والنسب,
                l.المستوى,
                l.القسم,
                l.رت as الرقم_الترتيبي
            FROM السجل_العام s
            LEFT JOIN اللوائح l ON s.الرمز = l.الرمز
            WHERE s.الرمز = ? AND l.السنة_الدراسية = ?
            LIMIT 1
            """
            
            cursor.execute(central_query, (student_code, self.school_year))
            result = cursor.fetchone()

            if not result:
                # استعلام بديل بدون شرط السنة الدراسية
                cursor.execute("""
                    SELECT 
                        s.الرمز,
                        s.الاسم_والنسب,
                        l.المستوى,
                        l.القسم,
                        l.رت as الرقم_الترتيبي
                    FROM السجل_العام s
                    LEFT JOIN اللوائح l ON s.الرمز = l.الرمز
                    WHERE s.الرمز = ?
                    ORDER BY l.السنة_الدراسية DESC
                    LIMIT 1
                """, (student_code,))
                result = cursor.fetchone()

            conn.close()
            
            if result:
                code = result[0] if result[0] else "غير محدد"
                name = result[1] if result[1] else "غير محدد"
                level = result[2] if result[2] else "غير محدد"
                class_name = result[3] if result[3] else "غير محدد"
                student_id = result[4] if result[4] else "غير محدد"
                
                print(f"✅ تم العثور على التلميذ: {name}")
                
                # تعيين معلومات التلميذ في HTML
                safe_code = str(code).replace("'", "\\'").replace('"', '\\"')
                safe_name = str(name).replace("'", "\\'").replace('"', '\\"')
                safe_id = str(student_id).replace("'", "\\'").replace('"', '\\"')
                safe_level = str(level).replace("'", "\\'").replace('"', '\\"')
                safe_class = str(class_name).replace("'", "\\'").replace('"', '\\"')
                
                js_code = f"setStudentInfo('{safe_code}', '{safe_name}', '{safe_id}', '{safe_level}', '{safe_class}');"
                self.web_view.page().runJavaScript(js_code)
                
                # حفظ البيانات في الكائن الحالي
                self.current_student = {
                    'code': code,
                    'name': name,
                    'id': student_id,
                    'level': level,
                    'class': class_name
                }
                
                self.status_bar.showMessage(f"تم العثور على التلميذ: {name}")
                return True
                
            else:
                print(f"❌ لم يتم العثور على التلميذ برمز: {student_code}")
                js_code = f"""
                    clearStudentInfo();
                    showAlert('لم يتم العثور على تلميذ برمز: {student_code} في قاعدة البيانات', 'danger');
                """
                self.web_view.page().runJavaScript(js_code)
                return False
                
        except Exception as e:
            print(f"❌ خطأ في البحث عن التلميذ: {e}")
            js_code = f"showAlert('حدث خطأ أثناء البحث: {str(e)}', 'danger');"
            self.web_view.page().runJavaScript(js_code)
            return False

    def handle_save_request(self, data_json):
        """معالجة طلب الحفظ من JavaScript"""
        try:
            import json
            data = json.loads(data_json)
            
            if not data.get('student_code') or not data.get('parent_name'):
                js_code = "confirmSaveError('الرجاء إدخال رمز التلميذ واسم الولي على الأقل');"
                self.web_view.page().runJavaScript(js_code)
                return False
            
            # حفظ البيانات في قاعدة البيانات
            visit_id = self.save_to_database(data)
            
            if visit_id:
                js_code = f"confirmSaveSuccess({visit_id});"
                
                # تحديث شريط الحالة
                student_name = data.get('student_name', 'غير محدد')
                parent_name = data.get('parent_name', 'غير محدد')
                current_time = datetime.now().strftime('%H:%M:%S')
                self.status_bar.showMessage(f"✅ تم حفظ زيارة {parent_name} للتلميذ {student_name} في {current_time}")
            else:
                js_code = "confirmSaveError('فشل في حفظ الزيارة في قاعدة البيانات');"
            
            self.web_view.page().runJavaScript(js_code)
            return visit_id is not None
            
        except Exception as e:
            print(f"❌ خطأ في معالجة طلب الحفظ: {e}")
            js_code = f"confirmSaveError('حدث خطأ في الحفظ: {str(e)}');"
            self.web_view.page().runJavaScript(js_code)
            return False

    def save_to_database(self, data):
        """حفظ البيانات في قاعدة البيانات"""
        try:
            print("💾 بدء حفظ بيانات الزيارة في قاعدة البيانات...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود الجدول وإنشاؤه إذا لزم الأمر
            cursor.execute("PRAGMA table_info(زيارة_ولي_الأمر)")
            columns = [col[1] for col in cursor.fetchall()]
            
            if not columns:
                cursor.execute('''
                    CREATE TABLE زيارة_ولي_الأمر (
                        الرقم INTEGER PRIMARY KEY AUTOINCREMENT,
                        الرمز TEXT,
                        اسم_التلميذ TEXT,
                        المستوى TEXT,
                        القسم TEXT,
                        رت TEXT,
                        اسم_الولي TEXT,
                        رقم_البطاقة TEXT,
                        تاريخ_الزيارة TEXT,
                        وقت_الزيارة TEXT,
                        سبب_الزيارة TEXT,
                        مضمون_الزيارة TEXT,
                        السنة_الدراسية TEXT,
                        الأسدس TEXT,
                        تاريخ_التسجيل TEXT
                    )
                ''')
            
            # إدراج البيانات
            cursor.execute('''
                INSERT INTO زيارة_ولي_الأمر (
                    الرمز, اسم_التلميذ, المستوى, القسم, رت,
                    اسم_الولي, رقم_البطاقة, تاريخ_الزيارة, وقت_الزيارة,
                    سبب_الزيارة, مضمون_الزيارة, السنة_الدراسية, الأسدس, تاريخ_التسجيل
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data.get('student_code', ''),
                data.get('student_name', ''),
                data.get('level', ''),
                data.get('class_name', ''),
                data.get('student_id', ''),
                data.get('parent_name', ''),
                data.get('id_card', ''),
                data.get('visit_date', ''),
                data.get('visit_time', ''),
                data.get('reason', ''),
                data.get('content', ''),
                data.get('school_year', self.school_year),
                data.get('semester', self.semester),
                datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            ))
            
            # الحصول على معرف الزيارة المدرجة
            visit_id = cursor.lastrowid
            
            conn.commit()
            conn.close()
            
            print("✅ تم حفظ زيارة ولي الأمر بنجاح في قاعدة البيانات")
            return visit_id
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")
            if 'conn' in locals():
                conn.close()
            return None


    def set_student_info(self, code, name, id_num, level, class_name):
        """تعيين معلومات التلميذ برمجياً"""
        try:
            print(f"📝 تعيين معلومات التلميذ: {name}")
            
            # حفظ البيانات في الكائن الحالي
            self.current_student = {
                'code': str(code) if code else '',
                'name': str(name) if name else '',
                'id': str(id_num) if id_num else '',
                'level': str(level) if level else '',
                'class': str(class_name) if class_name else ''
            }
            
            # تعيين البيانات في HTML
            safe_code = str(code).replace("'", "\\'").replace('"', '\\"') if code else ''
            safe_name = str(name).replace("'", "\\'").replace('"', '\\"') if name else ''
            safe_id = str(id_num).replace("'", "\\'").replace('"', '\\"') if id_num else ''
            safe_level = str(level).replace("'", "\\'").replace('"', '\\"') if level else ''
            safe_class = str(class_name).replace("'", "\\'").replace('"', '\\"') if class_name else ''
            
            js_code = f"setStudentInfo('{safe_code}', '{safe_name}', '{safe_id}', '{safe_level}', '{safe_class}');"
            self.web_view.page().runJavaScript(js_code)
            
            self.status_bar.showMessage(f"تم تحديد التلميذ: {name}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تعيين معلومات التلميذ: {e}")
            return False

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        # إيقاف جميع المؤقتات
        if hasattr(self, 'search_timer'):
            self.search_timer.stop()
        if hasattr(self, 'save_timer'):
            self.save_timer.stop()
        event.accept()


class ParentVisitWidget(QWidget):
    """ويدجت يمكن تضمينه في واجهات أخرى"""
    
    def __init__(self, db_path=None, parent=None):
        super().__init__(parent)
        self.db_path = db_path if db_path is not None else get_database_path()
        
        # إنشاء التخطيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء النافذة كعنصر داخلي
        self.visit_window = ParentVisitHTMLWindow(db_path, parent=self)
        
        # إضافة الويدجت المركزي
        central_widget = self.visit_window.centralWidget()
        layout.addWidget(central_widget)
    
    def set_student_info(self, code, name, id_num, level, class_name):
        """إعادة توجيه تعيين معلومات التلميذ"""
        return self.visit_window.set_student_info(code, name, id_num, level, class_name)


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    import sys
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = ParentVisitHTMLWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
