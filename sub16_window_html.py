#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import json
from datetime import datetime
from database_config import get_database_path, get_database_connection
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtSql import *

class ParentVisitViewerWindow(QMainWindow):
    """نافذة عرض سجلات زيارة أولياء الأمور - تستخدم منهجية Python + HTML الحديثة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # المتغيرات الأساسية
        self.parent_window = parent
        self.visits_data = []
        self.filtered_data = []
        
        # إعداد النافذة
        self.setupUI()

        # تحميل البيانات
        self.load_visits_data()

        # التأكد من فتح النافذة في كامل الشاشة
        self.showMaximized()

    def showEvent(self, event):
        """التأكد من فتح النافذة في كامل الشاشة عند عرضها"""
        super().showEvent(event)
        self.showMaximized()

    def resizeEvent(self, event):
        """منع تصغير النافذة والحفاظ على كامل الشاشة"""
        super().resizeEvent(event)
    
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        # إعدادات النافذة الأساسية
        self.setWindowTitle(" سجل زيارة أولياء الأمور - عرض  ")
        self.setLayoutDirection(Qt.RightToLeft)
        
        # فتح النافذة في كامل الشاشة
        self.showMaximized()
        
        # إضافة أيقونة للنافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # شريط الأدوات العلوي
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_frame.setFixedHeight(70)  # تثبيت ارتفاع الشريط العلوي
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #8E24AA,
                    stop: 1 #7B1FA2
                );
                border-radius: 10px;
                padding: 10px;
                min-height: 70px;
                max-height: 70px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)
        toolbar_layout.setSpacing(15)
        
        # شريط البحث برمز التلميذ فقط
        search_label = QLabel("🔍 البحث برمز التلميذ:")
        search_label.setFixedHeight(35)
        search_label.setStyleSheet("""
            color: white; 
            font-weight: bold; 
            font-size: 14px;
            min-height: 35px;
            max-height: 35px;
            padding: 5px;
        """)
        toolbar_layout.addWidget(search_label)
        
        self.search_entry = QLineEdit()
        self.search_entry.setPlaceholderText("أدخل رمز التلميذ للبحث...")
        self.search_entry.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid #6A1B9A;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-width: 300px;
            }
            QLineEdit:focus {
                border-color: #4A148C;
                box-shadow: 0 0 5px rgba(74, 20, 140, 0.5);
            }
        """)
        self.search_entry.textChanged.connect(self.on_search)
        toolbar_layout.addWidget(self.search_entry)
        
        # فلتر القسم
        section_label = QLabel("📚 القسم:")
        section_label.setFixedHeight(35)
        section_label.setStyleSheet("""
            color: white; 
            font-weight: bold; 
            font-size: 14px;
            min-height: 35px;
            max-height: 35px;
            padding: 5px;
        """)
        toolbar_layout.addWidget(section_label)
        
        self.section_combo = QComboBox()
        self.section_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 2px solid #6A1B9A;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-width: 150px;
            }
            QComboBox:focus {
                border-color: #4A148C;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
        """)
        self.section_combo.currentTextChanged.connect(self.on_filter)
        toolbar_layout.addWidget(self.section_combo)
        
        toolbar_layout.addStretch()
        
        # زر تحديث البيانات
        self.refresh_button = QPushButton("🔄 تحديث")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4CAF50,
                    stop: 1 #45a049
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5cbf60,
                    stop: 1 #4CAF50
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3d8b40,
                    stop: 1 #2e7d32
                );
            }
        """)
        self.refresh_button.clicked.connect(self.load_visits_data)
        toolbar_layout.addWidget(self.refresh_button)
        
        main_layout.addWidget(toolbar_frame)
        
        # منطقة عرض HTML
        self.web_view = QWebEngineView()
        self.web_view.setStyleSheet("""
            QWebEngineView {
                border: 2px solid #8E24AA;
                border-radius: 10px;
                background: white;
            }
        """)
        main_layout.addWidget(self.web_view)
        
        # شريط الأزرار السفلي
        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.StyledPanel)
        buttons_frame.setFixedHeight(80)  # تثبيت ارتفاع الشريط السفلي
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #37474f,
                    stop: 1 #263238
                );
                border-radius: 10px;
                padding: 5px;
                min-height: 80px;
                max-height: 80px;
            }
        """)
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(15, 10, 15, 10)
        buttons_layout.setSpacing(15)
        
        # زر طباعة التقرير
        self.print_button = QPushButton("🖨️ طباعة التقرير")
        self.print_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.print_button.setMinimumHeight(40)
        self.print_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #FF9800,
                    stop: 1 #F57C00
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #FFB74D,
                    stop: 1 #FF9800
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #F57C00,
                    stop: 1 #E65100
                );
            }
        """)
        self.print_button.clicked.connect(self.print_report)
        buttons_layout.addWidget(self.print_button)
        
        # زر طباعة سجلات الزيارة
        self.print_visits_button = QPushButton("📋 طباعة سجلات الزيارة")
        self.print_visits_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.print_visits_button.setMinimumHeight(40)
        self.print_visits_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2196F3,
                    stop: 1 #1976D2
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #64B5F6,
                    stop: 1 #2196F3
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #1976D2,
                    stop: 1 #0D47A1
                );
            }
        """)
        self.print_visits_button.clicked.connect(self.print_visits_report)
        buttons_layout.addWidget(self.print_visits_button)
        
        # زر طباعة تقرير شامل لجميع التلاميذ
        self.print_all_students_button = QPushButton("📊 تقرير شامل لجميع التلاميذ")
        self.print_all_students_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.print_all_students_button.setMinimumHeight(40)
        self.print_all_students_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #673AB7,
                    stop: 1 #512DA8
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #9575CD,
                    stop: 1 #673AB7
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #512DA8,
                    stop: 1 #311B92
                );
            }
        """)
        self.print_all_students_button.clicked.connect(self.print_all_students_report)
        buttons_layout.addWidget(self.print_all_students_button)
        
        # زر حذف الزيارات المحددة
        self.delete_button = QPushButton("🗑️ حذف المحددة")
        self.delete_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.delete_button.setMinimumHeight(40)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f44336,
                    stop: 1 #d32f2f
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f66356,
                    stop: 1 #f44336
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #c62828,
                    stop: 1 #b71c1c
                );
            }
        """)
        self.delete_button.clicked.connect(self.delete_selected_visits)
        buttons_layout.addWidget(self.delete_button)
        
        buttons_layout.addStretch()
        
        main_layout.addWidget(buttons_frame)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                border-top: 1px solid #ddd;
                font-size: 12px;
                color: #666;
                padding: 5px;
            }
        """)
        self.status_bar.showMessage("جاهز...")
    
    def load_visits_data(self):
        """تحميل بيانات زيارة أولياء الأمور من قاعدة البيانات"""
        try:
            self.status_bar.showMessage("جاري تحميل البيانات...")
            
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()
            
            # تحميل جميع زيارات أولياء الأمور
            cursor.execute('''
                SELECT الرقم, القسم, الرمز, رت, اسم_التلميذ, اسم_الولي,
                       رقم_البطاقة, وقت_الزيارة, تاريخ_الزيارة, سبب_الزيارة
                FROM زيارة_ولي_الأمر ORDER BY تاريخ_الزيارة DESC, وقت_الزيارة DESC
            ''')
            
            self.visits_data = cursor.fetchall()
            
            # تحميل قائمة الأقسام للتصفية
            cursor.execute('SELECT DISTINCT القسم FROM زيارة_ولي_الأمر WHERE القسم IS NOT NULL AND القسم != ""')
            sections = [row[0] for row in cursor.fetchall()]
            self.section_combo.clear()
            self.section_combo.addItems(['الكل'] + sorted(sections))
            
            conn.close()
            
            # تطبيق الفلاتر وإنشاء HTML
            self.apply_filters()
            
            self.status_bar.showMessage(f"تم تحميل {len(self.visits_data)} زيارة")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.status_bar.showMessage(f"خطأ في تحميل البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات:\n{str(e)}")
    
    def apply_filters(self):
        """تطبيق الفلاتر على البيانات"""
        search_term = self.search_entry.text().strip()
        section_filter = self.section_combo.currentText()
        
        self.filtered_data = []
        for row in self.visits_data:
            # تطبيق فلتر القسم (الفهرس 1)
            if section_filter != 'الكل' and str(row[1]) != section_filter:
                continue
                
            # تطبيق البحث برمز التلميذ فقط (الفهرس 2)
            if search_term:
                student_code = str(row[2] or '').strip()
                if search_term.lower() not in student_code.lower():
                    continue
                    
            self.filtered_data.append(row)
        
        # إنشاء وعرض HTML
        self.generate_html()
    
    def on_search(self):
        """معالج البحث"""
        self.apply_filters()
    
    def on_filter(self):
        """معالج التصفية"""
        self.apply_filters()
    
    def generate_html(self):
        """توليد HTML لعرض زيارات أولياء الأمور"""
        data_to_display = self.filtered_data
        
        html_content = self.create_html_template(data_to_display)
        self.web_view.setHtml(html_content)
    
    def create_html_template(self, data):
        """إنشاء قالب HTML حديث"""
        html = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل زيارة أولياء الأمور</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #8E24AA 0%, #7B1FA2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(45deg, #8E24AA, #9C27B0);
            color: white;
            padding: 30px;
            text-align: center;
            min-height: 180px;
            max-height: 180px;
            height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }}
        
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
        }}
        
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
        }}
        
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #8E24AA;
        }}
        
        .table-container {{
            padding: 20px;
            overflow-x: auto;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}
        
        th {{
            background: linear-gradient(45deg, #8E24AA, #9C27B0);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }}
        
        td {{
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }}
        
        tr:hover td {{
            background-color: #f8f9fa;
        }}
        
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        
        .visit-id {{
            background: #8E24AA;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
        }}
        
        .student-name {{
            font-weight: bold;
            color: #2c3e50;
        }}
        
        .parent-name {{
            font-weight: bold;
            color: #8E24AA;
        }}
        
        .date {{
            color: #7f8c8d;
            font-size: 0.9em;
        }}
        
        .time {{
            background: #27ae60;
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.8em;
        }}
        
        .section {{
            background: #e74c3c;
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.8em;
        }}
        
        .reason {{
            background: #f39c12;
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.8em;
        }}
        
        .record-checkbox {{
            text-align: center;
            width: 60px;
        }}
        
        .record-select {{
            width: 18px;
            height: 18px;
            cursor: pointer;
            transform: scale(1.2);
        }}
        
        #select-all {{
            width: 18px;
            height: 18px;
            cursor: pointer;
            transform: scale(1.2);
        }}
        
        .record-row.selected {{
            background-color: #f3e5f5 !important;
            border-right: 4px solid #8E24AA;
        }}
        
        .empty-state {{
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }}
        
        .empty-icon {{
            font-size: 4em;
            margin-bottom: 20px;
            display: block;
            opacity: 0.5;
        }}
        
        @media (max-width: 768px) {{
            .header {{
                min-height: 160px;
                max-height: 160px;
                height: 160px;
                padding: 20px;
            }}
            
            .header h1 {{
                font-size: 1.8em;
            }}
            
            table {{
                font-size: 0.8em;
            }}
            
            th, td {{
                padding: 8px;
            }}
        }}
    </style>
    
    <script>
        function toggleSelectAll() {{
            const selectAllBox = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.record-select');
            
            checkboxes.forEach(checkbox => {{
                checkbox.checked = selectAllBox.checked;
                updateRowSelection(checkbox);
            }});
        }}
        
        function updateRowSelection(checkbox) {{
            const row = checkbox.closest('tr');
            if (checkbox.checked) {{
                row.classList.add('selected');
            }} else {{
                row.classList.remove('selected');
            }}
        }}
        
        function getSelectedVisits() {{
            const selectedCheckboxes = document.querySelectorAll('.record-select:checked');
            const selectedVisits = [];
            
            selectedCheckboxes.forEach(checkbox => {{
                selectedVisits.push({{
                    id: checkbox.getAttribute('data-id'),
                    studentName: checkbox.getAttribute('data-student-name'),
                    parentName: checkbox.getAttribute('data-parent-name'),
                    date: checkbox.getAttribute('data-date')
                }});
            }});
            
            return selectedVisits;
        }}
        
        document.addEventListener('DOMContentLoaded', function() {{
            const checkboxes = document.querySelectorAll('.record-select');
            checkboxes.forEach(checkbox => {{
                checkbox.addEventListener('change', function() {{
                    updateRowSelection(this);
                    
                    const allCheckboxes = document.querySelectorAll('.record-select');
                    const checkedBoxes = document.querySelectorAll('.record-select:checked');
                    const selectAllBox = document.getElementById('select-all');
                    
                    if (checkedBoxes.length === 0) {{
                        selectAllBox.indeterminate = false;
                        selectAllBox.checked = false;
                    }} else if (checkedBoxes.length === allCheckboxes.length) {{
                        selectAllBox.indeterminate = false;
                        selectAllBox.checked = true;
                    }} else {{
                        selectAllBox.indeterminate = true;
                    }}
                }});
            }});
        }});
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1> سجل زيارة أولياء الأمور</h1>
            <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{len(data)}</div>
                <div>إجمالي الزيارات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len(set(row[4] for row in data if row[4]))}</div>
                <div>عدد التلاميذ</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len(set(row[6] for row in data if row[6]))}</div>
                <div>أولياء الأمور</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len(set(row[1] for row in data if row[1]))}</div>
                <div>الأقسام</div>
            </div>
        </div>
        
        <div class="table-container">
        """
        
        if data:
            html += f"""
            <table>
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="select-all" onclick="toggleSelectAll()">
                            اختيار الكل
                        </th>
                        <th>الرقم</th>
                        <th>القسم</th>
                        <th>رمز التلميذ</th>
                        <th>ر.ت</th>
                        <th>اسم التلميذ</th>
                        <th>اسم ولي الأمر</th>
                        <th>رقم البطاقة</th>
                        <th>وقت الزيارة</th>
                        <th>تاريخ الزيارة</th>
                        <th>سبب الزيارة</th>
                    </tr>
                </thead>
                <tbody>
            """
            
            for row in data:
                html += f"""
                    <tr class="record-row">
                        <td class="record-checkbox">
                            <input type="checkbox" class="record-select" 
                                   data-id="{row[0] or ''}"
                                   data-student-name="{row[4] or ''}"
                                   data-parent-name="{row[5] or ''}"
                                   data-date="{row[8] or ''}">
                        </td>
                        <td><span class="visit-id">{row[0] or 'غير محدد'}</span></td>
                        <td><span class="section">{row[1] or 'غير محدد'}</span></td>
                        <td>{row[2] or 'غير محدد'}</td>
                        <td>{row[3] or 'غير محدد'}</td>
                        <td class="student-name">{row[4] or 'غير محدد'}</td>
                        <td class="parent-name">{row[5] or 'غير محدد'}</td>
                        <td>{row[6] or 'غير محدد'}</td>
                        <td><span class="time">{row[7] or 'غير محدد'}</span></td>
                        <td class="date">{row[8] or 'غير محدد'}</td>
                        <td><span class="reason">{row[9] or 'غير محدد'}</span></td>
                    </tr>
                """
            
            html += """
                </tbody>
            </table>
            """
        else:
            html += """
            <div class="empty-state">
                <span class="empty-icon">👨‍👩‍👧</span>
                <h3>لا توجد زيارات</h3>
                <p>لم يتم العثور على أي زيارات تطابق معايير البحث</p>
            </div>
            """
        
        html += """
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def delete_selected_visits(self):
        """حذف الزيارات المحددة"""
        try:
            # تأكيد الحذف
            reply = QMessageBox.question(
                self, 
                "تأكيد الحذف", 
                "هل أنت متأكد من حذف زيارات أولياء الأمور المحددة؟\n\n⚠️ هذا الإجراء لا يمكن التراجع عنه!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # تنفيذ جافا سكريبت للحصول على الزيارات المحددة
            js_code = """
            (function() {
                const selectedVisits = getSelectedVisits();
                return JSON.stringify(selectedVisits);
            })();
            """
            
            def handle_selected_visits(result):
                try:
                    if not result:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي زيارات للحذف!")
                        return
                    
                    selected_visits = json.loads(result)
                    if not selected_visits:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي زيارات للحذف!")
                        return
                    
                    # حذف الزيارات من قاعدة البيانات
                    conn = sqlite3.connect(get_database_path())
                    cursor = conn.cursor()
                    deleted_count = 0
                    
                    for visit in selected_visits:
                        cursor.execute(
                            "DELETE FROM زيارة_ولي_الأمر WHERE الرقم = ?",
                            (visit['id'],)
                        )
                        if cursor.rowcount > 0:
                            deleted_count += 1
                    
                    conn.commit()
                    conn.close()
                    
                    # إظهار نتيجة الحذف
                    if deleted_count > 0:
                        QMessageBox.information(
                            self, 
                            "نجح الحذف", 
                            f"تم حذف {deleted_count} زيارة بنجاح! ✅"
                        )
                        # إعادة تحميل البيانات
                        self.load_visits_data()
                        self.status_bar.showMessage(f"تم حذف {deleted_count} زيارة")
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف الزيارات!")
                        
                except Exception as e:
                    print(f"خطأ في معالجة الزيارات المحددة: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف:\n{str(e)}")
            
            # تنفيذ الجافا سكريبت
            self.web_view.page().runJavaScript(js_code, handle_selected_visits)
            
        except Exception as e:
            print(f"خطأ في حذف الزيارات: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف:\n{str(e)}")
    
    def print_report(self):
        """طباعة التقرير باستخدام print7.py"""
        try:
            # الحصول على الزيارات المحددة من الجدول
            js_code = """
            (function() {
                const selectedVisits = getSelectedVisits();
                return JSON.stringify(selectedVisits);
            })();
            """
            
            def handle_selected_visits(result):
                try:
                    if not result:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي زيارات للطباعة!")
                        return
                    
                    selected_visits = json.loads(result)
                    if not selected_visits:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي زيارات للطباعة!")
                        return
                    
                    # استيراد دالة الطباعة من print7.py
                    try:
                        from print7 import print_parent_visit_sheet
                        
                        # طباعة كل زيارة محددة
                        printed_count = 0
                        for visit in selected_visits:
                            visit_id = visit['id']
                            try:
                                success = print_parent_visit_sheet(visit_id)
                                if success:
                                    printed_count += 1
                            except Exception as e:
                                print(f"خطأ في طباعة الزيارة {visit_id}: {e}")
                                continue
                        
                        if printed_count > 0:
                            QMessageBox.information(
                                self, 
                                "نجحت الطباعة", 
                                f"تم طباعة {printed_count} من {len(selected_visits)} زيارة بنجاح! ✅"
                            )
                            self.status_bar.showMessage(f"تم طباعة {printed_count} زيارة")
                        else:
                            QMessageBox.warning(self, "تحذير", "فشل في طباعة جميع الزيارات!")
                            
                    except ImportError as e:
                        print(f"خطأ في استيراد print7.py: {e}")
                        QMessageBox.critical(self, "خطأ", f"فشل في استيراد ملف الطباعة:\n{str(e)}")
                        
                except Exception as e:
                    print(f"خطأ في معالجة الزيارات المحددة: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الطباعة:\n{str(e)}")
            
            # تنفيذ الجافا سكريبت للحصول على الزيارات المحددة
            self.web_view.page().runJavaScript(js_code, handle_selected_visits)
            
        except Exception as e:
            print(f"خطأ في طباعة التقرير: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التقرير:\n{str(e)}")
    
    def print_visits_report(self):
        """طباعة سجلات الزيارة باستخدام print8.py"""
        try:
            # الحصول على الزيارات المحددة من الجدول
            js_code = """
            (function() {
                const selectedVisits = getSelectedVisits();
                return JSON.stringify(selectedVisits);
            })();
            """
            
            def handle_selected_visits(result):
                try:
                    if not result:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي زيارات للطباعة!")
                        return
                    
                    selected_visits = json.loads(result)
                    if not selected_visits:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي زيارات للطباعة!")
                        return
                    
                    # إذا كانت زيارة واحدة فقط، نحصل على رمز التلميذ
                    if len(selected_visits) == 1:
                        # الحصول على رمز التلميذ من الزيارة المحددة
                        visit_id = selected_visits[0]['id']
                        
                        # البحث عن رمز التلميذ من البيانات
                        student_code = None
                        for row in self.filtered_data:
                            if str(row[0]) == str(visit_id):  # الرقم هو الفهرس 0
                                student_code = row[2]  # رمز التلميذ هو الفهرس 2
                                break
                        
                        if student_code:
                            # استيراد دالة الطباعة من print8.py
                            try:
                                from print8 import print_parent_visits_report
                                
                                # طباعة تقرير سجلات الزيارات للتلميذ
                                success, file_path = print_parent_visits_report(student_code)
                                
                                if success:
                                    QMessageBox.information(
                                        self, 
                                        "نجحت الطباعة", 
                                        f"تم إنشاء تقرير سجلات الزيارات بنجاح! ✅"
                                    )
                                    self.status_bar.showMessage("تم إنشاء تقرير سجلات الزيارات")
                                else:
                                    QMessageBox.warning(self, "تحذير", "فشل في إنشاء تقرير سجلات الزيارات!")
                                    
                            except ImportError as e:
                                print(f"خطأ في استيراد print8.py: {e}")
                                QMessageBox.critical(self, "خطأ", f"فشل في استيراد ملف الطباعة:\n{str(e)}")
                        else:
                            QMessageBox.warning(self, "خطأ", "لم يتم العثور على رمز التلميذ!")
                    else:
                        QMessageBox.information(
                            self, 
                            "تنبيه", 
                            "يرجى تحديد زيارة واحدة فقط لطباعة سجلات الزيارات!\n\n" +
                            "هذا التقرير مخصص لعرض جميع زيارات تلميذ واحد."
                        )
                        
                except Exception as e:
                    print(f"خطأ في معالجة الزيارات المحددة: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الطباعة:\n{str(e)}")
            
            # تنفيذ الجافا سكريبت للحصول على الزيارات المحددة
            self.web_view.page().runJavaScript(js_code, handle_selected_visits)
            
        except Exception as e:
            print(f"خطأ في طباعة سجلات الزيارة: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة سجلات الزيارة:\n{str(e)}")

    def print_all_students_report(self):
        """طباعة تقرير شامل لجميع التلاميذ باستخدام print8.py"""
        try:
            # تأكيد إنشاء التقرير الشامل
            reply = QMessageBox.question(
                self, 
                "تأكيد إنشاء التقرير الشامل", 
                "هل تريد إنشاء تقرير شامل لجميع زيارات جميع التلاميذ؟\n\n"
                "⚠️ قد يستغرق هذا الإجراء وقتاً طويلاً إذا كان لديك عدد كبير من الزيارات.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # الحصول على جميع التلاميذ الذين لديهم زيارات
            try:
                conn = sqlite3.connect(get_database_path())
                cursor = conn.cursor()
                
                # البحث عن جميع التلاميذ الذين لديهم زيارات
                cursor.execute("""
                    SELECT DISTINCT الرمز, اسم_التلميذ 
                    FROM زيارة_ولي_الأمر 
                    WHERE الرمز IS NOT NULL AND الرمز != ''
                    ORDER BY اسم_التلميذ
                """)
                
                students_with_visits = cursor.fetchall()
                conn.close()
                
                if not students_with_visits:
                    QMessageBox.information(
                        self, 
                        "تنبيه", 
                        "لا توجد زيارات متاحة لإنشاء التقرير الشامل!"
                    )
                    return
                
                # عرض نافذة تقدم العمل
                progress_dialog = QProgressDialog(
                    "جاري إنشاء التقرير الشامل...", 
                    "إلغاء", 
                    0, 
                    len(students_with_visits), 
                    self
                )
                progress_dialog.setWindowTitle("إنشاء التقرير الشامل")
                progress_dialog.setLayoutDirection(Qt.RightToLeft)
                progress_dialog.setModal(True)
                progress_dialog.show()
                
                # إنشاء ملف شامل باستخدام print8.py
                from print8 import generate_comprehensive_report
                
                success, file_path = generate_comprehensive_report(students_with_visits, progress_dialog)
                
                progress_dialog.close()
                
                if success:
                    QMessageBox.information(
                        self, 
                        "نجح إنشاء التقرير", 
                        f"تم إنشاء التقرير الشامل بنجاح! ✅\n\n"
                        f"عدد التلاميذ: {len(students_with_visits)}\n"
                        f"مسار الملف: {file_path}"
                    )
                    self.status_bar.showMessage("تم إنشاء التقرير الشامل لجميع التلاميذ")
                else:
                    QMessageBox.warning(self, "تحذير", "فشل في إنشاء التقرير الشامل!")
                    
            except ImportError as e:
                print(f"خطأ في استيراد print8.py: {e}")
                QMessageBox.critical(
                    self, 
                    "خطأ", 
                    f"فشل في استيراد ملف الطباعة:\n{str(e)}\n\n"
                    "تأكد من وجود ملف print8.py وأنه يحتوي على دالة generate_comprehensive_report"
                )
            except Exception as db_error:
                print(f"خطأ في قاعدة البيانات: {db_error}")
                QMessageBox.critical(self, "خطأ", f"خطأ في الوصول إلى قاعدة البيانات:\n{str(db_error)}")
                
        except Exception as e:
            print(f"خطأ في إنشاء التقرير الشامل: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير الشامل:\n{str(e)}")

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        event.accept()

    def ensure_maximized(self):
        """دالة مساعدة لضمان فتح النافذة في كامل الشاشة"""
        self.showMaximized()
        self.activateWindow()
        self.raise_()


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = ParentVisitViewerWindow()
    window.ensure_maximized()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
