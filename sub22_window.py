"""
نافذة إدارة المهام المدرسية
نظام شامل لإدارة وتتبع المهام المدرسية مع تقارير احترافية
"""

import sys
import os
import sqlite3
from datetime import datetime, date, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                           QTableWidget, QTableWidgetItem, QHeaderView, QLabel,
                           QLineEdit, QTextEdit, QComboBox, QDateEdit, QCalendarWidget,
                           QMessageBox, QDialog, QFormLayout, QDialogButtonBox,
                           QProgressBar, QFrame, QSplitter, QTabWidget, QCheckBox,
                           QSpinBox, QGroupBox, QGridLayout, QScrollArea, QTreeWidget,
                           QTreeWidgetItem, QFileDialog, QApplication)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QPainter
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
from database_config import get_database_path, get_database_connection
import json
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib import colors
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

class TaskDialog(QDialog):
    """نافذة حوار إضافة/تعديل المهام"""
    
    def __init__(self, parent=None, task_data=None):
        super().__init__(parent)
        self.task_data = task_data
        self.is_edit_mode = task_data is not None
        self.setup_ui()
        
        if self.is_edit_mode:
            self.load_task_data()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل المهمة" if self.is_edit_mode else "إضافة مهمة جديدة"
        self.setWindowTitle(title)
        self.setFixedSize(1200, 700)
        self.setModal(True)
        self.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout()
        
        # تطبيق تصميم احترافي
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
                font-size: 14px;
            }
            QLabel {
                font-weight: bold;
                color: #2c3e50;
                margin: 8px 0;
                font-size: 16px;
                padding: 5px;
                text-align: right;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            }
            QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox {
                padding: 15px 20px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                font-size: 16px;
                color: #2c3e50;
                min-height: 30px;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
                text-align: right;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus, QSpinBox:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QComboBox::drop-down {
                border: none;
                width: 35px;
                background: #3498db;
                border-radius: 0 6px 6px 0;
            }
            QComboBox::down-arrow {
                width: 14px;
                height: 14px;
                background: white;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #3498db;
                background-color: white;
                selection-background-color: #3498db;
                font-size: 15px;
                padding: 8px;
                text-align: right;
            }
            QPushButton {
                padding: 18px 35px;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 16px;
                min-width: 150px;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            }
            QPushButton:hover {
                margin-top: -2px;
                margin-bottom: 2px;
            }
            QFormLayout {
                spacing: 15px;
            }
        """)
        
        # عنوان النافذة
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 22px; font-weight: bold; color: #2c3e50; margin: 15px 0; padding: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)
        form_layout.setSpacing(20)
        form_layout.setHorizontalSpacing(30)
        form_layout.setVerticalSpacing(15)
        
        # إنشاء تخطيط شبكي للعناصر
        grid_widget = QWidget()
        grid_widget.setStyleSheet("background-color: white; border-radius: 10px; padding: 20px;")
        grid_layout = QGridLayout(grid_widget)
        grid_layout.setSpacing(20)
        grid_layout.setHorizontalSpacing(25)
        
        # الصف الأول - عنوان المهمة والفئة
        title_label = QLabel("عنوان المهمة:")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 8px; background: transparent;")
        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("أدخل عنوان المهمة...")
        self.title_input.setStyleSheet("background-color: white; border: 2px solid #bdc3c7; border-radius: 8px; padding: 15px; font-size: 16px;")
        
        category_label = QLabel("فئة المهمة:")
        category_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 8px; background: transparent;")
        self.category_input = QComboBox()
        self.category_input.addItems([
            "إدارية", "تعليمية", "تربوية", "اجتماعية", 
            "تقنية", "صحية", "أمنية", "رياضية", "ثقافية", "أخرى"
        ])
        self.category_input.setEditable(True)
        self.category_input.setStyleSheet("background-color: white; border: 2px solid #bdc3c7; border-radius: 8px; padding: 15px; font-size: 16px;")
        
        grid_layout.addWidget(title_label, 0, 0)
        grid_layout.addWidget(self.title_input, 0, 1)
        grid_layout.addWidget(category_label, 0, 2)
        grid_layout.addWidget(self.category_input, 0, 3)
        
        # الصف الثاني - الأولوية والحالة
        priority_label = QLabel("الأولوية:")
        priority_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 8px; background: transparent;")
        self.priority_input = QComboBox()
        self.priority_input.addItems(["عادية", "مهمة", "عاجلة", "حرجة"])
        self.priority_input.setStyleSheet("background-color: white; border: 2px solid #bdc3c7; border-radius: 8px; padding: 15px; font-size: 16px;")
        
        status_label = QLabel("الحالة:")
        status_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 8px; background: transparent;")
        self.status_input = QComboBox()
        self.status_input.addItems(["مخطط لها", "قيد التنفيذ", "مكتملة", "متأخرة", "ملغاة"])
        self.status_input.setStyleSheet("background-color: white; border: 2px solid #bdc3c7; border-radius: 8px; padding: 15px; font-size: 16px;")
        
        grid_layout.addWidget(priority_label, 1, 0)
        grid_layout.addWidget(self.priority_input, 1, 1)
        grid_layout.addWidget(status_label, 1, 2)
        grid_layout.addWidget(self.status_input, 1, 3)
        
        # الصف الثالث - التواريخ
        start_date_label = QLabel("تاريخ البداية:")
        start_date_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 8px; background: transparent;")
        self.start_date_input = QDateEdit()
        self.start_date_input.setDate(QDate.currentDate())
        self.start_date_input.setCalendarPopup(True)
        self.start_date_input.setStyleSheet("background-color: white; border: 2px solid #bdc3c7; border-radius: 8px; padding: 15px; font-size: 16px;")
        
        end_date_label = QLabel("تاريخ الانتهاء:")
        end_date_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 8px; background: transparent;")
        self.end_date_input = QDateEdit()
        self.end_date_input.setDate(QDate.currentDate().addDays(7))
        self.end_date_input.setCalendarPopup(True)
        self.end_date_input.setStyleSheet("background-color: white; border: 2px solid #bdc3c7; border-radius: 8px; padding: 15px; font-size: 16px;")
        
        grid_layout.addWidget(start_date_label, 2, 0)
        grid_layout.addWidget(self.start_date_input, 2, 1)
        grid_layout.addWidget(end_date_label, 2, 2)
        grid_layout.addWidget(self.end_date_input, 2, 3)
        
        # الصف الرابع - المسؤول ونسبة الإنجاز
        responsible_label = QLabel("المسؤول:")
        responsible_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 8px; background: transparent;")
        self.responsible_input = QLineEdit()
        self.responsible_input.setPlaceholderText("اسم المسؤول عن تنفيذ المهمة...")
        self.responsible_input.setStyleSheet("background-color: white; border: 2px solid #bdc3c7; border-radius: 8px; padding: 15px; font-size: 16px;")
        
        progress_label = QLabel("نسبة الإنجاز:")
        progress_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 8px; background: transparent;")
        self.progress_input = QSpinBox()
        self.progress_input.setRange(0, 100)
        self.progress_input.setSuffix("%")
        self.progress_input.setStyleSheet("background-color: white; border: 2px solid #bdc3c7; border-radius: 8px; padding: 15px; font-size: 16px;")
        
        grid_layout.addWidget(responsible_label, 3, 0)
        grid_layout.addWidget(self.responsible_input, 3, 1)
        grid_layout.addWidget(progress_label, 3, 2)
        grid_layout.addWidget(self.progress_input, 3, 3)
        
        # وصف المهمة - يمتد على كامل العرض
        description_label = QLabel("وصف المهمة:")
        description_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 8px; background: transparent;")
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("وصف تفصيلي للمهمة...")
        self.description_input.setMaximumHeight(120)
        self.description_input.setStyleSheet("background-color: white; border: 2px solid #bdc3c7; border-radius: 8px; padding: 15px; font-size: 16px;")
        
        grid_layout.addWidget(description_label, 4, 0, 1, 1)
        grid_layout.addWidget(self.description_input, 4, 1, 1, 3)
        
        # ملاحظات - يمتد على كامل العرض
        notes_label = QLabel("ملاحظات:")
        notes_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 8px; background: transparent;")
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("ملاحظات إضافية...")
        self.notes_input.setMaximumHeight(100)
        self.notes_input.setStyleSheet("background-color: white; border: 2px solid #bdc3c7; border-radius: 8px; padding: 15px; font-size: 16px;")
        
        grid_layout.addWidget(notes_label, 5, 0, 1, 1)
        grid_layout.addWidget(self.notes_input, 5, 1, 1, 3)
        
        layout.addWidget(grid_widget)
        
        # أزرار التحكم
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("background-color: white; border-radius: 10px; padding: 15px; margin-top: 10px;")
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(20)
        
        save_button = QPushButton("💾 حفظ")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60; 
                color: white; 
                border: none; 
                border-radius: 8px; 
                padding: 15px 40px; 
                font-size: 16px; 
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        save_button.clicked.connect(self.save_task)
        
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c; 
                color: white; 
                border: none; 
                border-radius: 8px; 
                padding: 15px 40px; 
                font-size: 16px; 
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addStretch()
        
        layout.addWidget(buttons_frame)
        self.setLayout(layout)
        
    def load_task_data(self):
        """تحميل بيانات المهمة للتعديل"""
        if self.task_data:
            self.title_input.setText(self.task_data.get('العنوان', ''))
            self.description_input.setPlainText(self.task_data.get('الوصف', ''))
            self.category_input.setCurrentText(self.task_data.get('الفئة', ''))
            self.priority_input.setCurrentText(self.task_data.get('الأولوية', ''))
            self.responsible_input.setText(self.task_data.get('المسؤول', ''))
            self.status_input.setCurrentText(self.task_data.get('الحالة', ''))
            self.progress_input.setValue(self.task_data.get('نسبة_الإنجاز', 0))
            self.notes_input.setPlainText(self.task_data.get('ملاحظات', ''))
            
            # تحديد التواريخ
            if self.task_data.get('تاريخ_البداية'):
                start_date = datetime.strptime(self.task_data['تاريخ_البداية'], '%Y-%m-%d').date()
                self.start_date_input.setDate(QDate(start_date))
                
            if self.task_data.get('تاريخ_الانتهاء'):
                end_date = datetime.strptime(self.task_data['تاريخ_الانتهاء'], '%Y-%m-%d').date()
                self.end_date_input.setDate(QDate(end_date))
    
    def save_task(self):
        """حفظ بيانات المهمة"""
        # التحقق من صحة البيانات
        if not self.title_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يجب إدخال عنوان المهمة")
            return
            
        if self.start_date_input.date() > self.end_date_input.date():
            QMessageBox.warning(self, "تحذير", "تاريخ البداية يجب أن يكون قبل تاريخ الانتهاء")
            return
        
        # إعداد البيانات
        self.task_result = {
            'العنوان': self.title_input.text().strip(),
            'الوصف': self.description_input.toPlainText().strip(),
            'الفئة': self.category_input.currentText(),
            'الأولوية': self.priority_input.currentText(),
            'تاريخ_البداية': self.start_date_input.date().toString('yyyy-MM-dd'),
            'تاريخ_الانتهاء': self.end_date_input.date().toString('yyyy-MM-dd'),
            'المسؤول': self.responsible_input.text().strip(),
            'الحالة': self.status_input.currentText(),
            'نسبة_الإنجاز': self.progress_input.value(),
            'ملاحظات': self.notes_input.toPlainText().strip(),
            'تاريخ_الإنشاء': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'تاريخ_التحديث': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        self.accept()

class ReportsDialog(QDialog):
    """نافذة التقارير الاحترافية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة التقارير"""
        self.setWindowTitle("تقارير المهام المدرسية")
        self.setFixedSize(1200, 700)
        self.setModal(True)
        self.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout()
        
        # تطبيق تصميم احترافي
        self.setStyleSheet("""
            QDialog {
                background-color: #ecf0f1;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
                font-size: 14px;
            }
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                background-color: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background-color: #34495e;
                color: white;
                padding: 15px 25px;
                margin: 2px;
                border-radius: 8px 8px 0 0;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
            }
            QTabBar::tab:hover:!selected {
                background-color: #4a5568;
            }
            QLabel {
                font-size: 15px;
                color: #2c3e50;
                padding: 5px;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            }
            QTableWidget {
                font-size: 14px;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 15px;
                border: none;
                font-weight: bold;
                font-size: 14px;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            }
        """)
        
        # عنوان
        title_label = QLabel("📊 تقارير المهام المدرسية")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #2c3e50; margin: 15px; padding: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # تبويبات التقارير
        tabs = QTabWidget()
        
        # تبويب التقرير العام
        general_tab = self.create_general_report_tab()
        tabs.addTab(general_tab, "التقرير العام")
        
        # تبويب تقرير الأولويات
        priority_tab = self.create_priority_report_tab()
        tabs.addTab(priority_tab, "تقرير الأولويات")
        
        # تبويب تقرير التقدم
        progress_tab = self.create_progress_report_tab()
        tabs.addTab(progress_tab, "تقرير التقدم")
        
        # تبويب تقرير المسؤولين
        responsible_tab = self.create_responsible_report_tab()
        tabs.addTab(responsible_tab, "تقرير المسؤولين")
        
        layout.addWidget(tabs)
        
        # أزرار التصدير
        export_frame = QFrame()
        export_frame.setStyleSheet("background-color: white; border-radius: 10px; padding: 15px; margin-top: 10px;")
        export_layout = QHBoxLayout(export_frame)
        export_layout.setSpacing(15)
        
        pdf_button = QPushButton("📄 تصدير PDF")
        pdf_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c; 
                color: white; 
                padding: 15px 25px; 
                font-weight: bold; 
                font-size: 15px; 
                border-radius: 8px;
                min-width: 140px;
                border: none;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        pdf_button.clicked.connect(self.export_to_pdf)
        
        excel_button = QPushButton("📊 تصدير Excel")
        excel_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60; 
                color: white; 
                padding: 15px 25px; 
                font-weight: bold; 
                font-size: 15px; 
                border-radius: 8px;
                min-width: 140px;
                border: none;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        excel_button.clicked.connect(self.export_to_excel)
        
        print_button = QPushButton("🖨️ طباعة")
        print_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db; 
                color: white; 
                padding: 15px 25px; 
                font-weight: bold; 
                font-size: 15px; 
                border-radius: 8px;
                min-width: 140px;
                border: none;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        print_button.clicked.connect(self.print_report)
        
        close_button = QPushButton("❌ إغلاق")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6; 
                color: white; 
                padding: 15px 25px; 
                font-weight: bold; 
                font-size: 15px; 
                border-radius: 8px;
                min-width: 140px;
                border: none;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_button.clicked.connect(self.close)
        
        export_layout.addWidget(pdf_button)
        export_layout.addWidget(excel_button)
        export_layout.addWidget(print_button)
        export_layout.addStretch()
        export_layout.addWidget(close_button)
        
        layout.addWidget(export_frame)
        self.setLayout(layout)
        
    def create_general_report_tab(self):
        """إنشاء تبويب التقرير العام"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إحصائيات عامة
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: white; 
                border: 2px solid #bdc3c7; 
                border-radius: 12px; 
                padding: 20px;
                margin: 10px 0;
            }
        """)
        stats_layout = QGridLayout()
        stats_layout.setSpacing(15)
        stats_layout.setHorizontalSpacing(30)
        
        try:
            conn = get_database_connection()
            cursor = conn.cursor()
            
            # إجمالي المهام
            cursor.execute("SELECT COUNT(*) FROM المهام_المدرسية")
            total_tasks = cursor.fetchone()[0]
            
            # المهام المكتملة
            cursor.execute("SELECT COUNT(*) FROM المهام_المدرسية WHERE الحالة = 'مكتملة'")
            completed_tasks = cursor.fetchone()[0]
            
            # المهام قيد التنفيذ
            cursor.execute("SELECT COUNT(*) FROM المهام_المدرسية WHERE الحالة = 'قيد التنفيذ'")
            in_progress_tasks = cursor.fetchone()[0]
            
            # المهام المتأخرة
            cursor.execute("SELECT COUNT(*) FROM المهام_المدرسية WHERE الحالة = 'متأخرة'")
            overdue_tasks = cursor.fetchone()[0]
            
            conn.close()
            
            # عرض الإحصائيات
            label1 = QLabel(f"📋 إجمالي المهام: {total_tasks}")
            label1.setStyleSheet("""
                QLabel {
                    font-size: 18px; 
                    font-weight: bold; 
                    color: #2c3e50; 
                    padding: 15px; 
                    background-color: #ecf0f1; 
                    border-radius: 8px;
                    text-align: center;
                }
            """)
            
            label2 = QLabel(f"✅ المهام المكتملة: {completed_tasks}")
            label2.setStyleSheet("""
                QLabel {
                    font-size: 18px; 
                    font-weight: bold; 
                    color: #27ae60; 
                    padding: 15px; 
                    background-color: #d5f4e6; 
                    border-radius: 8px;
                    text-align: center;
                }
            """)
            
            label3 = QLabel(f"🔄 قيد التنفيذ: {in_progress_tasks}")
            label3.setStyleSheet("""
                QLabel {
                    font-size: 18px; 
                    font-weight: bold; 
                    color: #3498db; 
                    padding: 15px; 
                    background-color: #d6eaf8; 
                    border-radius: 8px;
                    text-align: center;
                }
            """)
            
            label4 = QLabel(f"⚠️ المتأخرة: {overdue_tasks}")
            label4.setStyleSheet("""
                QLabel {
                    font-size: 18px; 
                    font-weight: bold; 
                    color: #e74c3c; 
                    padding: 15px; 
                    background-color: #fadbd8; 
                    border-radius: 8px;
                    text-align: center;
                }
            """)
            
            stats_layout.addWidget(label1, 0, 0)
            stats_layout.addWidget(label2, 0, 1)
            stats_layout.addWidget(label3, 1, 0)
            stats_layout.addWidget(label4, 1, 1)
            
        except Exception as e:
            error_label = QLabel(f"خطأ في تحميل الإحصائيات: {str(e)}")
            error_label.setStyleSheet("font-size: 14px; color: #e74c3c; padding: 8px;")
            stats_layout.addWidget(error_label, 0, 0)
        
        stats_frame.setLayout(stats_layout)
        layout.addWidget(stats_frame)
        
        # جدول التقرير العام
        table = QTableWidget()
        self.load_general_report_data(table)
        layout.addWidget(table)
        
        widget.setLayout(layout)
        return widget
        
    def create_priority_report_tab(self):
        """إنشاء تبويب تقرير الأولويات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # جدول تقرير الأولويات
        table = QTableWidget()
        self.load_priority_report_data(table)
        layout.addWidget(table)
        
        widget.setLayout(layout)
        return widget
        
    def create_progress_report_tab(self):
        """إنشاء تبويب تقرير التقدم"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # جدول تقرير التقدم
        table = QTableWidget()
        self.load_progress_report_data(table)
        layout.addWidget(table)
        
        widget.setLayout(layout)
        return widget
        
    def create_responsible_report_tab(self):
        """إنشاء تبويب تقرير المسؤولين"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # جدول تقرير المسؤولين
        table = QTableWidget()
        self.load_responsible_report_data(table)
        layout.addWidget(table)
        
        widget.setLayout(layout)
        return widget
    
    def load_general_report_data(self, table):
        """تحميل بيانات التقرير العام"""
        try:
            conn = get_database_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT العنوان, الفئة, الأولوية, المسؤول, الحالة, 
                       نسبة_الإنجاز, تاريخ_البداية, تاريخ_الانتهاء
                FROM المهام_المدرسية 
                ORDER BY تاريخ_الإنشاء DESC
            """)
            
            tasks = cursor.fetchall()
            conn.close()
            
            # إعداد الجدول
            headers = ["العنوان", "الفئة", "الأولوية", "المسؤول", "الحالة", 
                      "نسبة الإنجاز", "تاريخ البداية", "تاريخ الانتهاء"]
            
            table.setColumnCount(len(headers))
            table.setRowCount(len(tasks))
            table.setHorizontalHeaderLabels(headers)
            
            # ملء البيانات
            for row, task in enumerate(tasks):
                for col, value in enumerate(task):
                    if col == 5:  # نسبة الإنجاز
                        item = QTableWidgetItem(f"{value}%")
                        item.setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
                    else:
                        item = QTableWidgetItem(str(value) if value else "")
                        item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                    table.setItem(row, col, item)
            
            # تنسيق الجدول
            table.horizontalHeader().setStretchLastSection(True)
            table.setAlternatingRowColors(True)
            table.setStyleSheet("""
                QTableWidget {
                    font-size: 15px;
                    font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
                    gridline-color: #ecf0f1;
                    background-color: white;
                }
                QTableWidget::item {
                    padding: 15px 10px;
                    border-bottom: 1px solid #ecf0f1;
                    color: #2c3e50;
                    text-align: right;
                }
                QHeaderView::section {
                    background-color: #34495e;
                    color: white;
                    padding: 18px 12px;
                    border: none;
                    font-weight: bold;
                    font-size: 15px;
                    font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
                    text-align: center;
                }
            """)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
    
    def load_priority_report_data(self, table):
        """تحميل بيانات تقرير الأولويات"""
        try:
            conn = get_database_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT الأولوية, COUNT(*) as العدد,
                       AVG(نسبة_الإنجاز) as متوسط_الإنجاز
                FROM المهام_المدرسية 
                GROUP BY الأولوية
                ORDER BY 
                    CASE الأولوية 
                        WHEN 'حرجة' THEN 1
                        WHEN 'عاجلة' THEN 2
                        WHEN 'مهمة' THEN 3
                        WHEN 'عادية' THEN 4
                        ELSE 5
                    END
            """)
            
            data = cursor.fetchall()
            conn.close()
            
            # إعداد الجدول
            headers = ["الأولوية", "عدد المهام", "متوسط الإنجاز %"]
            
            table.setColumnCount(len(headers))
            table.setRowCount(len(data))
            table.setHorizontalHeaderLabels(headers)
            
            # ملء البيانات
            for row, item in enumerate(data):
                item1 = QTableWidgetItem(str(item[0]))
                item1.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                table.setItem(row, 0, item1)
                
                item2 = QTableWidgetItem(str(item[1]))
                item2.setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
                table.setItem(row, 1, item2)
                
                item3 = QTableWidgetItem(f"{item[2]:.1f}%" if item[2] else "0%")
                item3.setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
                table.setItem(row, 2, item3)
            
            table.horizontalHeader().setStretchLastSection(True)
            table.setAlternatingRowColors(True)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
    
    def load_progress_report_data(self, table):
        """تحميل بيانات تقرير التقدم"""
        try:
            conn = get_database_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT العنوان, المسؤول, نسبة_الإنجاز, الحالة,
                       تاريخ_البداية, تاريخ_الانتهاء,
                       CASE 
                           WHEN تاريخ_الانتهاء < date('now') AND الحالة != 'مكتملة' 
                           THEN 'متأخرة'
                           ELSE 'في الوقت المحدد'
                       END as حالة_التوقيت
                FROM المهام_المدرسية 
                ORDER BY نسبة_الإنجاز DESC
            """)
            
            data = cursor.fetchall()
            conn.close()
            
            # إعداد الجدول
            headers = ["العنوان", "المسؤول", "نسبة الإنجاز", "الحالة", 
                      "تاريخ البداية", "تاريخ الانتهاء", "حالة التوقيت"]
            
            table.setColumnCount(len(headers))
            table.setRowCount(len(data))
            table.setHorizontalHeaderLabels(headers)
            
            # ملء البيانات
            for row, item in enumerate(data):
                for col, value in enumerate(item):
                    if col == 2:  # نسبة الإنجاز
                        table_item = QTableWidgetItem(f"{value}%")
                        table_item.setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
                    else:
                        table_item = QTableWidgetItem(str(value) if value else "")
                        table_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                    
                    # تلوين الصفوف حسب حالة التوقيت
                    if col == 6 and value == 'متأخرة':
                        table_item.setBackground(QColor("#ffebee"))
                    
                    table.setItem(row, col, table_item)
            
            table.horizontalHeader().setStretchLastSection(True)
            table.setAlternatingRowColors(True)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
    
    def load_responsible_report_data(self, table):
        """تحميل بيانات تقرير المسؤولين"""
        try:
            conn = get_database_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT المسؤول, 
                       COUNT(*) as إجمالي_المهام,
                       SUM(CASE WHEN الحالة = 'مكتملة' THEN 1 ELSE 0 END) as المهام_المكتملة,
                       AVG(نسبة_الإنجاز) as متوسط_الإنجاز,
                       SUM(CASE WHEN تاريخ_الانتهاء < date('now') AND الحالة != 'مكتملة' 
                           THEN 1 ELSE 0 END) as المهام_المتأخرة
                FROM المهام_المدرسية 
                WHERE المسؤول IS NOT NULL AND المسؤول != ''
                GROUP BY المسؤول
                ORDER BY إجمالي_المهام DESC
            """)
            
            data = cursor.fetchall()
            conn.close()
            
            # إعداد الجدول
            headers = ["المسؤول", "إجمالي المهام", "المهام المكتملة", 
                      "متوسط الإنجاز %", "المهام المتأخرة"]
            
            table.setColumnCount(len(headers))
            table.setRowCount(len(data))
            table.setHorizontalHeaderLabels(headers)
            
            # ملء البيانات
            for row, item in enumerate(data):
                table.setItem(row, 0, QTableWidgetItem(str(item[0])))
                table.item(row, 0).setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                
                table.setItem(row, 1, QTableWidgetItem(str(item[1])))
                table.item(row, 1).setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
                
                table.setItem(row, 2, QTableWidgetItem(str(item[2])))
                table.item(row, 2).setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
                
                table.setItem(row, 3, QTableWidgetItem(f"{item[3]:.1f}%" if item[3] else "0%"))
                table.item(row, 3).setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
                
                table.setItem(row, 4, QTableWidgetItem(str(item[4])))
                table.item(row, 4).setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
            
            table.horizontalHeader().setStretchLastSection(True)
            table.setAlternatingRowColors(True)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
    
    def export_to_pdf(self):
        """تصدير التقرير إلى PDF"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير PDF", 
                f"تقرير_المهام_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )
            
            if file_path:
                self.generate_pdf_report(file_path)
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير إلى: {file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تصدير PDF: {str(e)}")
    
    def export_to_excel(self):
        """تصدير التقرير إلى Excel"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير Excel", 
                f"تقرير_المهام_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )
            
            if file_path:
                self.generate_excel_report(file_path)
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير إلى: {file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تصدير Excel: {str(e)}")
    
    def print_report(self):
        """طباعة التقرير"""
        try:
            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)
            
            if print_dialog.exec_() == QPrintDialog.Accepted:
                self.print_pdf_report(printer)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في الطباعة: {str(e)}")
    
    def generate_pdf_report(self, file_path):
        """إنشاء تقرير PDF"""
        # سيتم تنفيذ هذه الوظيفة لاحقاً
        pass
    
    def generate_excel_report(self, file_path):
        """إنشاء تقرير Excel"""
        # سيتم تنفيذ هذه الوظيفة لاحقاً
        pass
    
    def print_pdf_report(self, printer):
        """طباعة التقرير"""
        # سيتم تنفيذ هذه الوظيفة لاحقاً
        pass

class SchoolTasksManager(QWidget):
    """نافذة إدارة المهام المدرسية الرئيسية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_database()
        self.setup_ui()
        self.load_tasks()
        
        # تحديث دوري للبيانات
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_data)
        self.timer.start(300000)  # كل 5 دقائق
        
    def setup_database(self):
        """إعداد جداول قاعدة البيانات"""
        try:
            conn = get_database_connection()
            cursor = conn.cursor()
            
            # إنشاء جدول المهام المدرسية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS المهام_المدرسية (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    العنوان TEXT NOT NULL,
                    الوصف TEXT,
                    الفئة TEXT,
                    الأولوية TEXT DEFAULT 'عادية',
                    تاريخ_البداية DATE,
                    تاريخ_الانتهاء DATE,
                    المسؤول TEXT,
                    الحالة TEXT DEFAULT 'مخطط لها',
                    نسبة_الإنجاز INTEGER DEFAULT 0,
                    ملاحظات TEXT,
                    تاريخ_الإنشاء DATETIME DEFAULT CURRENT_TIMESTAMP,
                    تاريخ_التحديث DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إنشاء جدول سجل التحديثات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS سجل_تحديثات_المهام (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    مهمة_id INTEGER,
                    نوع_التحديث TEXT,
                    الوصف TEXT,
                    المستخدم TEXT,
                    التاريخ DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (مهمة_id) REFERENCES المهام_المدرسية(id)
                )
            """)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ قاعدة البيانات", f"خطأ في إعداد قاعدة البيانات: {str(e)}")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم مع تصميم احترافي مطابق لـ sub19"""
        self.setWindowTitle("🎯 إدارة المهام المدرسية")
        self.setMinimumSize(1200, 700)
        self.resize(1400, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد الخط العام
        main_font = QFont("Calibri", 14)
        self.setFont(main_font)
        
        # تطبيق تصميم أنيق مشابه لـ sub19
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #667eea, stop:1 #764ba2);
                color: #333;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            }
            
            QMainWindow, QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #667eea, stop:1 #764ba2);
            }
            
            QFrame#main_container {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 20px;
                border: none;
            }
            
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px 30px;
                font-size: 15px;
                font-weight: bold;
                min-width: 160px;
                text-align: center;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2980b9, stop:1 #1f639a);
            }
            
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1f639a, stop:1 #154f7a);
            }
            
            QPushButton[class="success"] {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2ecc71, stop:1 #27ae60);
            }
            
            QPushButton[class="success"]:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #27ae60, stop:1 #1e8449);
            }
            
            QPushButton[class="warning"] {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f39c12, stop:1 #e67e22);
            }
            
            QPushButton[class="warning"]:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e67e22, stop:1 #d35400);
            }
            
            QPushButton[class="danger"] {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
            }
            
            QPushButton[class="danger"]:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
            }
            
            QPushButton[class="purple"] {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #9b59b6, stop:1 #8e44ad);
            }
            
            QPushButton[class="purple"]:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #8e44ad, stop:1 #7d3c98);
            }
            
            QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px 18px;
                font-size: 15px;
                background: white;
                color: #2c3e50;
                selection-background-color: #3498db;
                selection-color: white;
                min-height: 25px;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus, QSpinBox:focus {
                border-color: #3498db;
                outline: none;
                background: #f8f9fa;
            }
            
            QComboBox::drop-down {
                border: none;
                width: 35px;
                background: #3498db;
                border-radius: 0 6px 6px 0;
            }
            
            QComboBox::down-arrow {
                width: 14px;
                height: 14px;
                background: white;
            }
            
            QComboBox QAbstractItemView {
                border: 2px solid #3498db;
                background-color: white;
                selection-background-color: #3498db;
                font-size: 14px;
                padding: 5px;
            }
            
            QTableWidget {
                background: white;
                border: none;
                border-radius: 15px;
                gridline-color: #ecf0f1;
                selection-background-color: #3498db;
                selection-color: white;
                alternate-background-color: #f9f9f9;
            }
            
            QTableWidget::item {
                padding: 15px 12px;
                border-bottom: 1px solid #ecf0f1;
                font-size: 15px;
                font-weight: 500;
                color: #2c3e50;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
                text-align: right;
            }
            
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #34495e, stop:1 #2c3e50);
                color: white;
                padding: 20px 15px;
                border: none;
                font-weight: bold;
                font-size: 16px;
                text-align: center;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            }
            
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2c3e50, stop:1 #1b2631);
            }
            
            QTabWidget::pane {
                border: none;
                background: transparent;
            }
            
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #95a5a6, stop:1 #7f8c8d);
                color: white;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                font-weight: bold;
                min-width: 120px;
            }
            
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
            }
            
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #7f8c8d, stop:1 #6c7b7d);
            }
            
            QLabel {
                color: #2c3e50;
                font-weight: 600;
                font-size: 16px;
                padding: 5px;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
                text-align: right;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin: 10px 0;
                padding-top: 15px;
                background: rgba(255, 255, 255, 0.8);
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px;
                color: #2c3e50;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            }
            
            QCheckBox {
                font-weight: 500;
                color: #2c3e50;
                font-size: 15px;
                padding: 5px;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            }
            
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                background: white;
            }
            
            QCheckBox::indicator:checked {
                background: #3498db;
                border-color: #3498db;
            }
            
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 14px;
                border-radius: 7px;
            }
            
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 7px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: rgba(255, 255, 255, 0.5);
            }
            
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
            
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                text-align: center;
                background: white;
            }
            
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 6px;
            }
        """)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(0)
        
        # إنشاء الحاوية الرئيسية مع الخلفية البيضاء
        main_container = QFrame()
        main_container.setObjectName("main_container")
        container_layout = QVBoxLayout(main_container)
        container_layout.setContentsMargins(30, 30, 30, 30)
        container_layout.setSpacing(20)
        
        # إنشاء العنوان الرئيسي
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: transparent;
                border: none;
                border-bottom: 3px solid #4CAF50;
                margin-bottom: 10px;
                padding-bottom: 20px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(0, 0, 0, 20)
        
        title_label = QLabel("🎯 إدارة المهام المدرسية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #1e3a8a;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
                font-size: 32px;
                font-weight: bold;
                margin-bottom: 15px;
                background: transparent;
                border: none;
                padding: 10px;
            }
        """)
        header_layout.addWidget(title_label)
        
        subtitle_label = QLabel("نظام شامل لإدارة وتتبع المهام المدرسية مع تقارير احترافية")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 18px;
                font-weight: 500;
                background: transparent;
                border: none;
                padding: 5px;
                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            }
        """)
        header_layout.addWidget(subtitle_label)
        
        container_layout.addWidget(header_frame)
        
        # إنشاء منطقة الأزرار
        controls_frame = QFrame()
        controls_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.95);
                border: 1px solid #bdc3c7;
                border-radius: 15px;
                padding: 15px;
            }
        """)
        controls_layout = QHBoxLayout(controls_frame)
        controls_layout.setSpacing(15)
        
        # أزرار التحكم الرئيسية
        self.add_task_btn = QPushButton("➕ إضافة مهمة جديدة")
        self.add_task_btn.setProperty("class", "success")
        self.add_task_btn.clicked.connect(self.add_task)
        
        self.edit_task_btn = QPushButton("✏️ تعديل المهمة")
        self.edit_task_btn.clicked.connect(self.edit_task)
        
        self.delete_task_btn = QPushButton("🗑️ حذف المهمة")
        self.delete_task_btn.setProperty("class", "danger")
        self.delete_task_btn.clicked.connect(self.delete_task)
        
        self.reports_btn = QPushButton("📊 التقارير")
        self.reports_btn.setProperty("class", "purple")
        self.reports_btn.clicked.connect(self.show_reports)
        
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setProperty("class", "warning")
        self.refresh_btn.clicked.connect(self.refresh_data)
        
        # إضافة الأزرار للتخطيط
        controls_layout.addWidget(self.add_task_btn)
        controls_layout.addWidget(self.edit_task_btn)
        controls_layout.addWidget(self.delete_task_btn)
        controls_layout.addWidget(self.reports_btn)
        controls_layout.addWidget(self.refresh_btn)
        controls_layout.addStretch()
        
        container_layout.addWidget(controls_frame)
        
        # إنشاء منطقة الفلاتر
        filters_frame = QFrame()
        filters_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.9);
                border: 1px solid #bdc3c7;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        filters_layout = QHBoxLayout(filters_frame)
        filters_layout.setSpacing(15)
        
        # فلتر حسب الحالة
        status_label = QLabel("الحالة:")
        status_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 5px;")
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "مخطط لها", "قيد التنفيذ", "مكتملة", "متأخرة", "ملغاة"])
        self.status_filter.currentTextChanged.connect(self.apply_filters)
        
        # فلتر حسب الأولوية
        priority_label = QLabel("الأولوية:")
        priority_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 5px;")
        self.priority_filter = QComboBox()
        self.priority_filter.addItems(["الكل", "عادية", "مهمة", "عاجلة", "حرجة"])
        self.priority_filter.currentTextChanged.connect(self.apply_filters)
        
        # فلتر البحث
        search_label = QLabel("البحث:")
        search_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 5px;")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث في المهام...")
        self.search_input.textChanged.connect(self.apply_filters)
        
        filters_layout.addWidget(status_label)
        filters_layout.addWidget(self.status_filter)
        filters_layout.addWidget(priority_label)
        filters_layout.addWidget(self.priority_filter)
        filters_layout.addWidget(search_label)
        filters_layout.addWidget(self.search_input)
        filters_layout.addStretch()
        
        container_layout.addWidget(filters_frame)
        
        # إنشاء جدول المهام
        self.table = QTableWidget()
        self.table.setColumnCount(10)
        self.table.setHorizontalHeaderLabels([
            "ID", "العنوان", "الفئة", "الأولوية", "تاريخ البداية", 
            "تاريخ الانتهاء", "المسؤول", "الحالة", "نسبة الإنجاز", "تاريخ الإنشاء"
        ])
        
        # إعداد الجدول
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setSortingEnabled(True)
        
        # ضبط عرض الأعمدة
        header = self.table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(self.table.columnCount()):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)
        
        container_layout.addWidget(self.table)
        
        # شريط الحالة
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.9);
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px;
            }
        """)
        status_layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز")
        self.status_label.setStyleSheet("color: #7f8c8d; font-weight: 500; font-size: 15px; padding: 5px;")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        last_update_label = QLabel(f"آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        last_update_label.setStyleSheet("color: #95a5a6; font-size: 14px; padding: 5px;")
        status_layout.addWidget(last_update_label)
        
        container_layout.addWidget(status_frame)
        
        # إضافة الحاوية الرئيسية للتخطيط
        main_layout.addWidget(main_container)

    def apply_filters(self):
        """تطبيق المرشحات على الجدول"""
        status_filter = self.status_filter.currentText()
        priority_filter = self.priority_filter.currentText()
        search_text = self.search_input.text().lower()
        
        for row in range(self.table.rowCount()):
            show_row = True
            
            # فلتر الحالة
            if status_filter != "الكل":
                status_item = self.table.item(row, 7)  # عمود الحالة
                if status_item and status_item.text() != status_filter:
                    show_row = False
            
            # فلتر الأولوية
            if priority_filter != "الكل":
                priority_item = self.table.item(row, 3)  # عمود الأولوية
                if priority_item and priority_item.text() != priority_filter:
                    show_row = False
            
            # فلتر البحث
            if search_text:
                row_text = ""
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                if search_text not in row_text:
                    show_row = False
            
            self.table.setRowHidden(row, not show_row)

    def load_tasks(self):
        """تحميل المهام من قاعدة البيانات"""
        try:
            conn = get_database_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, العنوان, الفئة, الأولوية, تاريخ_البداية, 
                       تاريخ_الانتهاء, المسؤول, الحالة, نسبة_الإنجاز, تاريخ_الإنشاء
                FROM المهام_المدرسية 
                ORDER BY تاريخ_الإنشاء DESC
            """)
            
            tasks = cursor.fetchall()
            conn.close()
            
            # تحديث الجدول
            self.table.setRowCount(len(tasks))
            
            for row, task in enumerate(tasks):
                for col, value in enumerate(task):
                    if col == 8:  # نسبة الإنجاز
                        # إنشاء شريط تقدم
                        progress_widget = QWidget()
                        progress_layout = QHBoxLayout()
                        progress_layout.setContentsMargins(5, 2, 5, 2)
                        
                        progress_bar = QProgressBar()
                        progress_bar.setValue(value or 0)
                        progress_bar.setStyleSheet("""
                            QProgressBar {
                                border: 2px solid #bdc3c7;
                                border-radius: 5px;
                                background-color: #ecf0f1;
                                text-align: center;
                                height: 20px;
                                font-size: 13px;
                                font-weight: bold;
                                color: #2c3e50;
                                font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
                            }
                            QProgressBar::chunk {
                                background-color: #3498db;
                                border-radius: 3px;
                            }
                        """)
                        
                        progress_layout.addWidget(progress_bar)
                        progress_widget.setLayout(progress_layout)
                        
                        self.table.setCellWidget(row, col, progress_widget)
                    else:
                        item = QTableWidgetItem(str(value) if value else "")
                        item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                        
                        # تلوين الصفوف حسب الأولوية والحالة
                        if col == 3:  # الأولوية
                            if value == "حرجة":
                                item.setBackground(QColor("#ffebee"))
                            elif value == "عاجلة":
                                item.setBackground(QColor("#fff3e0"))
                            elif value == "مهمة":
                                item.setBackground(QColor("#f3e5f5"))
                        
                        if col == 7:  # الحالة
                            if value == "مكتملة":
                                item.setBackground(QColor("#e8f5e8"))
                            elif value == "متأخرة":
                                item.setBackground(QColor("#ffebee"))
                        
                        self.table.setItem(row, col, item)
            
            self.status_label.setText(f"تم تحميل {len(tasks)} مهمة")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المهام: {str(e)}")
            self.status_label.setText("خطأ في تحميل البيانات")
    
    def filter_tasks(self):
        """فلترة المهام حسب البحث والفلاتر"""
        search_text = self.search_input.text().lower()
        status_filter = self.status_filter.currentText()
        priority_filter = self.priority_filter.currentText()
        
        for row in range(self.table.rowCount()):
            show_row = True
            
            # فلتر البحث
            if search_text:
                title = self.table.item(row, 1).text().lower()
                responsible = self.table.item(row, 4).text().lower()
                if search_text not in title and search_text not in responsible:
                    show_row = False
            
            # فلتر الحالة
            if status_filter != "جميع الحالات":
                status = self.table.item(row, 5).text()
                if status != status_filter:
                    show_row = False
            
            # فلتر الأولوية
            if priority_filter != "جميع الأولويات":
                priority = self.table.item(row, 3).text()
                if priority != priority_filter:
                    show_row = False
            
            self.table.setRowHidden(row, not show_row)
    
    def add_task(self):
        """إضافة مهمة جديدة"""
        dialog = TaskDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                conn = get_database_connection()
                cursor = conn.cursor()
                
                task_data = dialog.task_result
                
                cursor.execute("""
                    INSERT INTO المهام_المدرسية 
                    (العنوان, الوصف, الفئة, الأولوية, تاريخ_البداية, تاريخ_الانتهاء,
                     المسؤول, الحالة, نسبة_الإنجاز, ملاحظات, تاريخ_الإنشاء, تاريخ_التحديث)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    task_data['العنوان'], task_data['الوصف'], task_data['الفئة'],
                    task_data['الأولوية'], task_data['تاريخ_البداية'], task_data['تاريخ_الانتهاء'],
                    task_data['المسؤول'], task_data['الحالة'], task_data['نسبة_الإنجاز'],
                    task_data['ملاحظات'], task_data['تاريخ_الإنشاء'], task_data['تاريخ_التحديث']
                ))
                
                conn.commit()
                conn.close()
                
                self.load_tasks()
                self.status_label.setText("تم إضافة المهمة بنجاح")
                QMessageBox.information(self, "نجح", "تم إضافة المهمة بنجاح")
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المهمة: {str(e)}")
    
    def edit_task(self):
        """تعديل المهمة المحددة"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مهمة للتعديل")
            return
        
        # الحصول على ID المهمة
        task_id = self.table.item(current_row, 0).text()
        
        try:
            conn = get_database_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM المهام_المدرسية WHERE id = ?", (task_id,))
            task_data = cursor.fetchone()
            conn.close()
            
            if task_data:
                # تحويل البيانات إلى قاموس
                columns = ['id', 'العنوان', 'الوصف', 'الفئة', 'الأولوية', 'تاريخ_البداية', 
                          'تاريخ_الانتهاء', 'المسؤول', 'الحالة', 'نسبة_الإنجاز', 'ملاحظات',
                          'تاريخ_الإنشاء', 'تاريخ_التحديث']
                task_dict = dict(zip(columns, task_data))
                
                dialog = TaskDialog(self, task_dict)
                if dialog.exec_() == QDialog.Accepted:
                    self.update_task(task_id, dialog.task_result)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات المهمة: {str(e)}")
    
    def update_task(self, task_id, task_data):
        """تحديث بيانات المهمة"""
        try:
            conn = get_database_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE المهام_المدرسية 
                SET العنوان=?, الوصف=?, الفئة=?, الأولوية=?, تاريخ_البداية=?, 
                    تاريخ_الانتهاء=?, المسؤول=?, الحالة=?, نسبة_الإنجاز=?, 
                    ملاحظات=?, تاريخ_التحديث=?
                WHERE id=?
            """, (
                task_data['العنوان'], task_data['الوصف'], task_data['الفئة'],
                task_data['الأولوية'], task_data['تاريخ_البداية'], task_data['تاريخ_الانتهاء'],
                task_data['المسؤول'], task_data['الحالة'], task_data['نسبة_الإنجاز'],
                task_data['ملاحظات'], task_data['تاريخ_التحديث'], task_id
            ))
            
            conn.commit()
            conn.close()
            
            self.load_tasks()
            self.status_label.setText("تم تحديث المهمة بنجاح")
            QMessageBox.information(self, "نجح", "تم تحديث المهمة بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث المهمة: {str(e)}")
    
    def delete_task(self):
        """حذف المهمة المحددة"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مهمة للحذف")
            return
        
        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف", 
            "هل أنت متأكد من حذف هذه المهمة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            task_id = self.table.item(current_row, 0).text()
            
            try:
                conn = get_database_connection()
                cursor = conn.cursor()
                
                cursor.execute("DELETE FROM المهام_المدرسية WHERE id = ?", (task_id,))
                conn.commit()
                conn.close()
                
                self.load_tasks()
                self.status_label.setText("تم حذف المهمة بنجاح")
                QMessageBox.information(self, "نجح", "تم حذف المهمة بنجاح")
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف المهمة: {str(e)}")
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_tasks()
        # تحديث معلومات سريعة في شريط العنوان
        for widget in self.findChildren(QLabel):
            if "إجمالي:" in widget.text():
                self.update_info_label(widget)
                break
        self.status_label.setText(f"تم التحديث: {datetime.now().strftime('%H:%M:%S')}")
    
    def show_reports(self):
        """عرض نافذة التقارير"""
        reports_dialog = ReportsDialog(self)
        reports_dialog.exec_()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # تطبيق خط عربي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    window = SchoolTasksManager()
    window.show()
    
    sys.exit(app.exec_())
